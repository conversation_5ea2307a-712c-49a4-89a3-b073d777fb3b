var Kv=Object.defineProperty;var Gv=(e,t,n)=>t in e?Kv(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Le=(e,t,n)=>Gv(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function op(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ap={exports:{}},ta={},lp={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=Symbol.for("react.element"),qv=Symbol.for("react.portal"),Qv=Symbol.for("react.fragment"),Xv=Symbol.for("react.strict_mode"),Yv=Symbol.for("react.profiler"),Zv=Symbol.for("react.provider"),Jv=Symbol.for("react.context"),ex=Symbol.for("react.forward_ref"),tx=Symbol.for("react.suspense"),nx=Symbol.for("react.memo"),rx=Symbol.for("react.lazy"),Jd=Symbol.iterator;function sx(e){return e===null||typeof e!="object"?null:(e=Jd&&e[Jd]||e["@@iterator"],typeof e=="function"?e:null)}var up={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cp=Object.assign,dp={};function es(e,t,n){this.props=e,this.context=t,this.refs=dp,this.updater=n||up}es.prototype.isReactComponent={};es.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};es.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fp(){}fp.prototype=es.prototype;function Yu(e,t,n){this.props=e,this.context=t,this.refs=dp,this.updater=n||up}var Zu=Yu.prototype=new fp;Zu.constructor=Yu;cp(Zu,es.prototype);Zu.isPureReactComponent=!0;var ef=Array.isArray,hp=Object.prototype.hasOwnProperty,Ju={current:null},mp={key:!0,ref:!0,__self:!0,__source:!0};function pp(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)hp.call(t,r)&&!mp.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:mi,type:e,key:i,ref:o,props:s,_owner:Ju.current}}function ix(e,t){return{$$typeof:mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ec(e){return typeof e=="object"&&e!==null&&e.$$typeof===mi}function ox(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var tf=/\/+/g;function Va(e,t){return typeof e=="object"&&e!==null&&e.key!=null?ox(""+e.key):t.toString(36)}function Xi(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case mi:case qv:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Va(o,0):r,ef(s)?(n="",e!=null&&(n=e.replace(tf,"$&/")+"/"),Xi(s,t,n,"",function(c){return c})):s!=null&&(ec(s)&&(s=ix(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(tf,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",ef(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+Va(i,a);o+=Xi(i,t,n,u,s)}else if(u=sx(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+Va(i,a++),o+=Xi(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Ci(e,t,n){if(e==null)return e;var r=[],s=0;return Xi(e,r,"","",function(i){return t.call(n,i,s++)}),r}function ax(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ge={current:null},Yi={transition:null},lx={ReactCurrentDispatcher:Ge,ReactCurrentBatchConfig:Yi,ReactCurrentOwner:Ju};function gp(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:Ci,forEach:function(e,t,n){Ci(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ci(e,function(){t++}),t},toArray:function(e){return Ci(e,function(t){return t})||[]},only:function(e){if(!ec(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=es;J.Fragment=Qv;J.Profiler=Yv;J.PureComponent=Yu;J.StrictMode=Xv;J.Suspense=tx;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lx;J.act=gp;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cp({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Ju.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)hp.call(t,u)&&!mp.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:mi,type:e.type,key:s,ref:i,props:r,_owner:o}};J.createContext=function(e){return e={$$typeof:Jv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Zv,_context:e},e.Consumer=e};J.createElement=pp;J.createFactory=function(e){var t=pp.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:ex,render:e}};J.isValidElement=ec;J.lazy=function(e){return{$$typeof:rx,_payload:{_status:-1,_result:e},_init:ax}};J.memo=function(e,t){return{$$typeof:nx,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Yi.transition;Yi.transition={};try{e()}finally{Yi.transition=t}};J.unstable_act=gp;J.useCallback=function(e,t){return Ge.current.useCallback(e,t)};J.useContext=function(e){return Ge.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Ge.current.useDeferredValue(e)};J.useEffect=function(e,t){return Ge.current.useEffect(e,t)};J.useId=function(){return Ge.current.useId()};J.useImperativeHandle=function(e,t,n){return Ge.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Ge.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Ge.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Ge.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Ge.current.useReducer(e,t,n)};J.useRef=function(e){return Ge.current.useRef(e)};J.useState=function(e){return Ge.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Ge.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Ge.current.useTransition()};J.version="18.3.1";lp.exports=J;var w=lp.exports;const Z=op(w);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ux=w,cx=Symbol.for("react.element"),dx=Symbol.for("react.fragment"),fx=Object.prototype.hasOwnProperty,hx=ux.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,mx={key:!0,ref:!0,__self:!0,__source:!0};function yp(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)fx.call(t,r)&&!mx.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:cx,type:e,key:i,ref:o,props:s,_owner:hx.current}}ta.Fragment=dx;ta.jsx=yp;ta.jsxs=yp;ap.exports=ta;var l=ap.exports,Pl={},vp={exports:{}},ct={},xp={exports:{}},wp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,W){var Y=L.length;L.push(W);e:for(;0<Y;){var ce=Y-1>>>1,ke=L[ce];if(0<s(ke,W))L[ce]=W,L[Y]=ke,Y=ce;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var W=L[0],Y=L.pop();if(Y!==W){L[0]=Y;e:for(var ce=0,ke=L.length,In=ke>>>1;ce<In;){var $t=2*(ce+1)-1,pr=L[$t],Ut=$t+1,an=L[Ut];if(0>s(pr,Y))Ut<ke&&0>s(an,pr)?(L[ce]=an,L[Ut]=Y,ce=Ut):(L[ce]=pr,L[$t]=Y,ce=$t);else if(Ut<ke&&0>s(an,Y))L[ce]=an,L[Ut]=Y,ce=Ut;else break e}}return W}function s(L,W){var Y=L.sortIndex-W.sortIndex;return Y!==0?Y:L.id-W.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],c=[],d=1,h=null,f=3,g=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(L){for(var W=n(c);W!==null;){if(W.callback===null)r(c);else if(W.startTime<=L)r(c),W.sortIndex=W.expirationTime,t(u,W);else break;W=n(c)}}function b(L){if(x=!1,y(L),!v)if(n(u)!==null)v=!0,H(C);else{var W=n(c);W!==null&&U(b,W.startTime-L)}}function C(L,W){v=!1,x&&(x=!1,p(T),T=-1),g=!0;var Y=f;try{for(y(W),h=n(u);h!==null&&(!(h.expirationTime>W)||L&&!I());){var ce=h.callback;if(typeof ce=="function"){h.callback=null,f=h.priorityLevel;var ke=ce(h.expirationTime<=W);W=e.unstable_now(),typeof ke=="function"?h.callback=ke:h===n(u)&&r(u),y(W)}else r(u);h=n(u)}if(h!==null)var In=!0;else{var $t=n(c);$t!==null&&U(b,$t.startTime-W),In=!1}return In}finally{h=null,f=Y,g=!1}}var j=!1,_=null,T=-1,E=5,D=-1;function I(){return!(e.unstable_now()-D<E)}function A(){if(_!==null){var L=e.unstable_now();D=L;var W=!0;try{W=_(!0,L)}finally{W?R():(j=!1,_=null)}}else j=!1}var R;if(typeof m=="function")R=function(){m(A)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,G=X.port2;X.port1.onmessage=A,R=function(){G.postMessage(null)}}else R=function(){S(A,0)};function H(L){_=L,j||(j=!0,R())}function U(L,W){T=S(function(){L(e.unstable_now())},W)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,H(C))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(L){switch(f){case 1:case 2:case 3:var W=3;break;default:W=f}var Y=f;f=W;try{return L()}finally{f=Y}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,W){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var Y=f;f=L;try{return W()}finally{f=Y}},e.unstable_scheduleCallback=function(L,W,Y){var ce=e.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?ce+Y:ce):Y=ce,L){case 1:var ke=-1;break;case 2:ke=250;break;case 5:ke=**********;break;case 4:ke=1e4;break;default:ke=5e3}return ke=Y+ke,L={id:d++,callback:W,priorityLevel:L,startTime:Y,expirationTime:ke,sortIndex:-1},Y>ce?(L.sortIndex=Y,t(c,L),n(u)===null&&L===n(c)&&(x?(p(T),T=-1):x=!0,U(b,Y-ce))):(L.sortIndex=ke,t(u,L),v||g||(v=!0,H(C))),L},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(L){var W=f;return function(){var Y=f;f=W;try{return L.apply(this,arguments)}finally{f=Y}}}})(wp);xp.exports=wp;var px=xp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gx=w,ot=px;function V(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sp=new Set,$s={};function dr(e,t){$r(e,t),$r(e+"Capture",t)}function $r(e,t){for($s[e]=t,e=0;e<t.length;e++)Sp.add(t[e])}var en=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Al=Object.prototype.hasOwnProperty,yx=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,nf={},rf={};function vx(e){return Al.call(rf,e)?!0:Al.call(nf,e)?!1:yx.test(e)?rf[e]=!0:(nf[e]=!0,!1)}function xx(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function wx(e,t,n,r){if(t===null||typeof t>"u"||xx(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function qe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var tc=/[\-:]([a-z])/g;function nc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(tc,nc);Re[t]=new qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(tc,nc);Re[t]=new qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(tc,nc);Re[t]=new qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new qe(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function rc(e,t,n,r){var s=Re.hasOwnProperty(t)?Re[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(wx(t,n,s,r)&&(n=null),r||s===null?vx(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var on=gx.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ji=Symbol.for("react.element"),vr=Symbol.for("react.portal"),xr=Symbol.for("react.fragment"),sc=Symbol.for("react.strict_mode"),Dl=Symbol.for("react.profiler"),bp=Symbol.for("react.provider"),kp=Symbol.for("react.context"),ic=Symbol.for("react.forward_ref"),Ml=Symbol.for("react.suspense"),Rl=Symbol.for("react.suspense_list"),oc=Symbol.for("react.memo"),fn=Symbol.for("react.lazy"),Tp=Symbol.for("react.offscreen"),sf=Symbol.iterator;function os(e){return e===null||typeof e!="object"?null:(e=sf&&e[sf]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Object.assign,Ia;function vs(e){if(Ia===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ia=t&&t[1]||""}return`
`+Ia+e}var Fa=!1;function Oa(e,t){if(!e||Fa)return"";Fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{Fa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vs(e):""}function Sx(e){switch(e.tag){case 5:return vs(e.type);case 16:return vs("Lazy");case 13:return vs("Suspense");case 19:return vs("SuspenseList");case 0:case 2:case 15:return e=Oa(e.type,!1),e;case 11:return e=Oa(e.type.render,!1),e;case 1:return e=Oa(e.type,!0),e;default:return""}}function Ll(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case xr:return"Fragment";case vr:return"Portal";case Dl:return"Profiler";case sc:return"StrictMode";case Ml:return"Suspense";case Rl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case kp:return(e.displayName||"Context")+".Consumer";case bp:return(e._context.displayName||"Context")+".Provider";case ic:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oc:return t=e.displayName||null,t!==null?t:Ll(e.type)||"Memo";case fn:t=e._payload,e=e._init;try{return Ll(e(t))}catch{}}return null}function bx(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ll(t);case 8:return t===sc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function jn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function kx(e){var t=Cp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ni(e){e._valueTracker||(e._valueTracker=kx(e))}function jp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Cp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function mo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Vl(e,t){var n=t.checked;return ge({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function of(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=jn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Np(e,t){t=t.checked,t!=null&&rc(e,"checked",t,!1)}function Il(e,t){Np(e,t);var n=jn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Fl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Fl(e,t.type,jn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function af(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Fl(e,t,n){(t!=="number"||mo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var xs=Array.isArray;function Lr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+jn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Ol(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(V(91));return ge({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function lf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(V(92));if(xs(n)){if(1<n.length)throw Error(V(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:jn(n)}}function _p(e,t){var n=jn(t.value),r=jn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function uf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ep(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ep(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var _i,Pp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(_i=_i||document.createElement("div"),_i.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=_i.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Us(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var js={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Tx=["Webkit","ms","Moz","O"];Object.keys(js).forEach(function(e){Tx.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),js[t]=js[e]})});function Ap(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||js.hasOwnProperty(e)&&js[e]?(""+t).trim():t+"px"}function Dp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Ap(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Cx=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function $l(e,t){if(t){if(Cx[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(V(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(V(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(V(61))}if(t.style!=null&&typeof t.style!="object")throw Error(V(62))}}function Ul(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Bl=null;function ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Hl=null,Vr=null,Ir=null;function cf(e){if(e=yi(e)){if(typeof Hl!="function")throw Error(V(280));var t=e.stateNode;t&&(t=oa(t),Hl(e.stateNode,e.type,t))}}function Mp(e){Vr?Ir?Ir.push(e):Ir=[e]:Vr=e}function Rp(){if(Vr){var e=Vr,t=Ir;if(Ir=Vr=null,cf(e),t)for(e=0;e<t.length;e++)cf(t[e])}}function Lp(e,t){return e(t)}function Vp(){}var za=!1;function Ip(e,t,n){if(za)return e(t,n);za=!0;try{return Lp(e,t,n)}finally{za=!1,(Vr!==null||Ir!==null)&&(Vp(),Rp())}}function Bs(e,t){var n=e.stateNode;if(n===null)return null;var r=oa(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(V(231,t,typeof n));return n}var Wl=!1;if(en)try{var as={};Object.defineProperty(as,"passive",{get:function(){Wl=!0}}),window.addEventListener("test",as,as),window.removeEventListener("test",as,as)}catch{Wl=!1}function jx(e,t,n,r,s,i,o,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var Ns=!1,po=null,go=!1,Kl=null,Nx={onError:function(e){Ns=!0,po=e}};function _x(e,t,n,r,s,i,o,a,u){Ns=!1,po=null,jx.apply(Nx,arguments)}function Ex(e,t,n,r,s,i,o,a,u){if(_x.apply(this,arguments),Ns){if(Ns){var c=po;Ns=!1,po=null}else throw Error(V(198));go||(go=!0,Kl=c)}}function fr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Fp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function df(e){if(fr(e)!==e)throw Error(V(188))}function Px(e){var t=e.alternate;if(!t){if(t=fr(e),t===null)throw Error(V(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return df(s),e;if(i===r)return df(s),t;i=i.sibling}throw Error(V(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(V(189))}}if(n.alternate!==r)throw Error(V(190))}if(n.tag!==3)throw Error(V(188));return n.stateNode.current===n?e:t}function Op(e){return e=Px(e),e!==null?zp(e):null}function zp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=zp(e);if(t!==null)return t;e=e.sibling}return null}var $p=ot.unstable_scheduleCallback,ff=ot.unstable_cancelCallback,Ax=ot.unstable_shouldYield,Dx=ot.unstable_requestPaint,Se=ot.unstable_now,Mx=ot.unstable_getCurrentPriorityLevel,lc=ot.unstable_ImmediatePriority,Up=ot.unstable_UserBlockingPriority,yo=ot.unstable_NormalPriority,Rx=ot.unstable_LowPriority,Bp=ot.unstable_IdlePriority,na=null,It=null;function Lx(e){if(It&&typeof It.onCommitFiberRoot=="function")try{It.onCommitFiberRoot(na,e,void 0,(e.current.flags&128)===128)}catch{}}var Pt=Math.clz32?Math.clz32:Fx,Vx=Math.log,Ix=Math.LN2;function Fx(e){return e>>>=0,e===0?32:31-(Vx(e)/Ix|0)|0}var Ei=64,Pi=4194304;function ws(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=ws(a):(i&=o,i!==0&&(r=ws(i)))}else o=n&~s,o!==0?r=ws(o):i!==0&&(r=ws(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Pt(t),s=1<<n,r|=e[n],t&=~s;return r}function Ox(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function zx(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Pt(i),a=1<<o,u=s[o];u===-1?(!(a&n)||a&r)&&(s[o]=Ox(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function Gl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hp(){var e=Ei;return Ei<<=1,!(Ei&4194240)&&(Ei=64),e}function $a(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function pi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Pt(t),e[t]=n}function $x(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Pt(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function uc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Pt(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var ne=0;function Wp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Kp,cc,Gp,qp,Qp,ql=!1,Ai=[],vn=null,xn=null,wn=null,Hs=new Map,Ws=new Map,mn=[],Ux="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function hf(e,t){switch(e){case"focusin":case"focusout":vn=null;break;case"dragenter":case"dragleave":xn=null;break;case"mouseover":case"mouseout":wn=null;break;case"pointerover":case"pointerout":Hs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ws.delete(t.pointerId)}}function ls(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=yi(t),t!==null&&cc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Bx(e,t,n,r,s){switch(t){case"focusin":return vn=ls(vn,e,t,n,r,s),!0;case"dragenter":return xn=ls(xn,e,t,n,r,s),!0;case"mouseover":return wn=ls(wn,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Hs.set(i,ls(Hs.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Ws.set(i,ls(Ws.get(i)||null,e,t,n,r,s)),!0}return!1}function Xp(e){var t=qn(e.target);if(t!==null){var n=fr(t);if(n!==null){if(t=n.tag,t===13){if(t=Fp(n),t!==null){e.blockedOn=t,Qp(e.priority,function(){Gp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ql(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Bl=r,n.target.dispatchEvent(r),Bl=null}else return t=yi(n),t!==null&&cc(t),e.blockedOn=n,!1;t.shift()}return!0}function mf(e,t,n){Zi(e)&&n.delete(t)}function Hx(){ql=!1,vn!==null&&Zi(vn)&&(vn=null),xn!==null&&Zi(xn)&&(xn=null),wn!==null&&Zi(wn)&&(wn=null),Hs.forEach(mf),Ws.forEach(mf)}function us(e,t){e.blockedOn===t&&(e.blockedOn=null,ql||(ql=!0,ot.unstable_scheduleCallback(ot.unstable_NormalPriority,Hx)))}function Ks(e){function t(s){return us(s,e)}if(0<Ai.length){us(Ai[0],e);for(var n=1;n<Ai.length;n++){var r=Ai[n];r.blockedOn===e&&(r.blockedOn=null)}}for(vn!==null&&us(vn,e),xn!==null&&us(xn,e),wn!==null&&us(wn,e),Hs.forEach(t),Ws.forEach(t),n=0;n<mn.length;n++)r=mn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<mn.length&&(n=mn[0],n.blockedOn===null);)Xp(n),n.blockedOn===null&&mn.shift()}var Fr=on.ReactCurrentBatchConfig,xo=!0;function Wx(e,t,n,r){var s=ne,i=Fr.transition;Fr.transition=null;try{ne=1,dc(e,t,n,r)}finally{ne=s,Fr.transition=i}}function Kx(e,t,n,r){var s=ne,i=Fr.transition;Fr.transition=null;try{ne=4,dc(e,t,n,r)}finally{ne=s,Fr.transition=i}}function dc(e,t,n,r){if(xo){var s=Ql(e,t,n,r);if(s===null)Ya(e,t,r,wo,n),hf(e,r);else if(Bx(s,e,t,n,r))r.stopPropagation();else if(hf(e,r),t&4&&-1<Ux.indexOf(e)){for(;s!==null;){var i=yi(s);if(i!==null&&Kp(i),i=Ql(e,t,n,r),i===null&&Ya(e,t,r,wo,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Ya(e,t,r,null,n)}}var wo=null;function Ql(e,t,n,r){if(wo=null,e=ac(r),e=qn(e),e!==null)if(t=fr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Fp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return wo=e,null}function Yp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Mx()){case lc:return 1;case Up:return 4;case yo:case Rx:return 16;case Bp:return 536870912;default:return 16}default:return 16}}var gn=null,fc=null,Ji=null;function Zp(){if(Ji)return Ji;var e,t=fc,n=t.length,r,s="value"in gn?gn.value:gn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Ji=s.slice(e,1<r?1-r:void 0)}function eo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Di(){return!0}function pf(){return!1}function dt(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Di:pf,this.isPropagationStopped=pf,this}return ge(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Di)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Di)},persist:function(){},isPersistent:Di}),t}var ts={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hc=dt(ts),gi=ge({},ts,{view:0,detail:0}),Gx=dt(gi),Ua,Ba,cs,ra=ge({},gi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==cs&&(cs&&e.type==="mousemove"?(Ua=e.screenX-cs.screenX,Ba=e.screenY-cs.screenY):Ba=Ua=0,cs=e),Ua)},movementY:function(e){return"movementY"in e?e.movementY:Ba}}),gf=dt(ra),qx=ge({},ra,{dataTransfer:0}),Qx=dt(qx),Xx=ge({},gi,{relatedTarget:0}),Ha=dt(Xx),Yx=ge({},ts,{animationName:0,elapsedTime:0,pseudoElement:0}),Zx=dt(Yx),Jx=ge({},ts,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),e1=dt(Jx),t1=ge({},ts,{data:0}),yf=dt(t1),n1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},r1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},s1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function i1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=s1[e])?!!t[e]:!1}function mc(){return i1}var o1=ge({},gi,{key:function(e){if(e.key){var t=n1[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=eo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?r1[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mc,charCode:function(e){return e.type==="keypress"?eo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?eo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),a1=dt(o1),l1=ge({},ra,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vf=dt(l1),u1=ge({},gi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mc}),c1=dt(u1),d1=ge({},ts,{propertyName:0,elapsedTime:0,pseudoElement:0}),f1=dt(d1),h1=ge({},ra,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),m1=dt(h1),p1=[9,13,27,32],pc=en&&"CompositionEvent"in window,_s=null;en&&"documentMode"in document&&(_s=document.documentMode);var g1=en&&"TextEvent"in window&&!_s,Jp=en&&(!pc||_s&&8<_s&&11>=_s),xf=" ",wf=!1;function eg(e,t){switch(e){case"keyup":return p1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tg(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wr=!1;function y1(e,t){switch(e){case"compositionend":return tg(t);case"keypress":return t.which!==32?null:(wf=!0,xf);case"textInput":return e=t.data,e===xf&&wf?null:e;default:return null}}function v1(e,t){if(wr)return e==="compositionend"||!pc&&eg(e,t)?(e=Zp(),Ji=fc=gn=null,wr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jp&&t.locale!=="ko"?null:t.data;default:return null}}var x1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Sf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!x1[e.type]:t==="textarea"}function ng(e,t,n,r){Mp(r),t=So(t,"onChange"),0<t.length&&(n=new hc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Es=null,Gs=null;function w1(e){hg(e,0)}function sa(e){var t=kr(e);if(jp(t))return e}function S1(e,t){if(e==="change")return t}var rg=!1;if(en){var Wa;if(en){var Ka="oninput"in document;if(!Ka){var bf=document.createElement("div");bf.setAttribute("oninput","return;"),Ka=typeof bf.oninput=="function"}Wa=Ka}else Wa=!1;rg=Wa&&(!document.documentMode||9<document.documentMode)}function kf(){Es&&(Es.detachEvent("onpropertychange",sg),Gs=Es=null)}function sg(e){if(e.propertyName==="value"&&sa(Gs)){var t=[];ng(t,Gs,e,ac(e)),Ip(w1,t)}}function b1(e,t,n){e==="focusin"?(kf(),Es=t,Gs=n,Es.attachEvent("onpropertychange",sg)):e==="focusout"&&kf()}function k1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return sa(Gs)}function T1(e,t){if(e==="click")return sa(t)}function C1(e,t){if(e==="input"||e==="change")return sa(t)}function j1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Dt=typeof Object.is=="function"?Object.is:j1;function qs(e,t){if(Dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Al.call(t,s)||!Dt(e[s],t[s]))return!1}return!0}function Tf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Cf(e,t){var n=Tf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Tf(n)}}function ig(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ig(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function og(){for(var e=window,t=mo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=mo(e.document)}return t}function gc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function N1(e){var t=og(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ig(n.ownerDocument.documentElement,n)){if(r!==null&&gc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Cf(n,i);var o=Cf(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _1=en&&"documentMode"in document&&11>=document.documentMode,Sr=null,Xl=null,Ps=null,Yl=!1;function jf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Yl||Sr==null||Sr!==mo(r)||(r=Sr,"selectionStart"in r&&gc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ps&&qs(Ps,r)||(Ps=r,r=So(Xl,"onSelect"),0<r.length&&(t=new hc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Sr)))}function Mi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var br={animationend:Mi("Animation","AnimationEnd"),animationiteration:Mi("Animation","AnimationIteration"),animationstart:Mi("Animation","AnimationStart"),transitionend:Mi("Transition","TransitionEnd")},Ga={},ag={};en&&(ag=document.createElement("div").style,"AnimationEvent"in window||(delete br.animationend.animation,delete br.animationiteration.animation,delete br.animationstart.animation),"TransitionEvent"in window||delete br.transitionend.transition);function ia(e){if(Ga[e])return Ga[e];if(!br[e])return e;var t=br[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ag)return Ga[e]=t[n];return e}var lg=ia("animationend"),ug=ia("animationiteration"),cg=ia("animationstart"),dg=ia("transitionend"),fg=new Map,Nf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function An(e,t){fg.set(e,t),dr(t,[e])}for(var qa=0;qa<Nf.length;qa++){var Qa=Nf[qa],E1=Qa.toLowerCase(),P1=Qa[0].toUpperCase()+Qa.slice(1);An(E1,"on"+P1)}An(lg,"onAnimationEnd");An(ug,"onAnimationIteration");An(cg,"onAnimationStart");An("dblclick","onDoubleClick");An("focusin","onFocus");An("focusout","onBlur");An(dg,"onTransitionEnd");$r("onMouseEnter",["mouseout","mouseover"]);$r("onMouseLeave",["mouseout","mouseover"]);$r("onPointerEnter",["pointerout","pointerover"]);$r("onPointerLeave",["pointerout","pointerover"]);dr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));dr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));dr("onBeforeInput",["compositionend","keypress","textInput","paste"]);dr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));dr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));dr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ss="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),A1=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ss));function _f(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ex(r,t,void 0,e),e.currentTarget=null}function hg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;_f(s,a,c),i=u}else for(o=0;o<r.length;o++){if(a=r[o],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;_f(s,a,c),i=u}}}if(go)throw e=Kl,go=!1,Kl=null,e}function oe(e,t){var n=t[nu];n===void 0&&(n=t[nu]=new Set);var r=e+"__bubble";n.has(r)||(mg(t,e,2,!1),n.add(r))}function Xa(e,t,n){var r=0;t&&(r|=4),mg(n,e,r,t)}var Ri="_reactListening"+Math.random().toString(36).slice(2);function Qs(e){if(!e[Ri]){e[Ri]=!0,Sp.forEach(function(n){n!=="selectionchange"&&(A1.has(n)||Xa(n,!1,e),Xa(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ri]||(t[Ri]=!0,Xa("selectionchange",!1,t))}}function mg(e,t,n,r){switch(Yp(t)){case 1:var s=Wx;break;case 4:s=Kx;break;default:s=dc}n=s.bind(null,t,n,e),s=void 0,!Wl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Ya(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;a!==null;){if(o=qn(a),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}a=a.parentNode}}r=r.return}Ip(function(){var c=i,d=ac(n),h=[];e:{var f=fg.get(e);if(f!==void 0){var g=hc,v=e;switch(e){case"keypress":if(eo(n)===0)break e;case"keydown":case"keyup":g=a1;break;case"focusin":v="focus",g=Ha;break;case"focusout":v="blur",g=Ha;break;case"beforeblur":case"afterblur":g=Ha;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=gf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Qx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=c1;break;case lg:case ug:case cg:g=Zx;break;case dg:g=f1;break;case"scroll":g=Gx;break;case"wheel":g=m1;break;case"copy":case"cut":case"paste":g=e1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=vf}var x=(t&4)!==0,S=!x&&e==="scroll",p=x?f!==null?f+"Capture":null:f;x=[];for(var m=c,y;m!==null;){y=m;var b=y.stateNode;if(y.tag===5&&b!==null&&(y=b,p!==null&&(b=Bs(m,p),b!=null&&x.push(Xs(m,b,y)))),S)break;m=m.return}0<x.length&&(f=new g(f,v,null,n,d),h.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==Bl&&(v=n.relatedTarget||n.fromElement)&&(qn(v)||v[tn]))break e;if((g||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=c,v=v?qn(v):null,v!==null&&(S=fr(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=c),g!==v)){if(x=gf,b="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(x=vf,b="onPointerLeave",p="onPointerEnter",m="pointer"),S=g==null?f:kr(g),y=v==null?f:kr(v),f=new x(b,m+"leave",g,n,d),f.target=S,f.relatedTarget=y,b=null,qn(d)===c&&(x=new x(p,m+"enter",v,n,d),x.target=y,x.relatedTarget=S,b=x),S=b,g&&v)t:{for(x=g,p=v,m=0,y=x;y;y=gr(y))m++;for(y=0,b=p;b;b=gr(b))y++;for(;0<m-y;)x=gr(x),m--;for(;0<y-m;)p=gr(p),y--;for(;m--;){if(x===p||p!==null&&x===p.alternate)break t;x=gr(x),p=gr(p)}x=null}else x=null;g!==null&&Ef(h,f,g,x,!1),v!==null&&S!==null&&Ef(h,S,v,x,!0)}}e:{if(f=c?kr(c):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var C=S1;else if(Sf(f))if(rg)C=C1;else{C=k1;var j=b1}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=T1);if(C&&(C=C(e,c))){ng(h,C,n,d);break e}j&&j(e,f,c),e==="focusout"&&(j=f._wrapperState)&&j.controlled&&f.type==="number"&&Fl(f,"number",f.value)}switch(j=c?kr(c):window,e){case"focusin":(Sf(j)||j.contentEditable==="true")&&(Sr=j,Xl=c,Ps=null);break;case"focusout":Ps=Xl=Sr=null;break;case"mousedown":Yl=!0;break;case"contextmenu":case"mouseup":case"dragend":Yl=!1,jf(h,n,d);break;case"selectionchange":if(_1)break;case"keydown":case"keyup":jf(h,n,d)}var _;if(pc)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else wr?eg(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Jp&&n.locale!=="ko"&&(wr||T!=="onCompositionStart"?T==="onCompositionEnd"&&wr&&(_=Zp()):(gn=d,fc="value"in gn?gn.value:gn.textContent,wr=!0)),j=So(c,T),0<j.length&&(T=new yf(T,e,null,n,d),h.push({event:T,listeners:j}),_?T.data=_:(_=tg(n),_!==null&&(T.data=_)))),(_=g1?y1(e,n):v1(e,n))&&(c=So(c,"onBeforeInput"),0<c.length&&(d=new yf("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=_))}hg(h,t)})}function Xs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function So(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Bs(e,n),i!=null&&r.unshift(Xs(e,i,s)),i=Bs(e,t),i!=null&&r.push(Xs(e,i,s))),e=e.return}return r}function gr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ef(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,s?(u=Bs(n,i),u!=null&&o.unshift(Xs(n,u,a))):s||(u=Bs(n,i),u!=null&&o.push(Xs(n,u,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var D1=/\r\n?/g,M1=/\u0000|\uFFFD/g;function Pf(e){return(typeof e=="string"?e:""+e).replace(D1,`
`).replace(M1,"")}function Li(e,t,n){if(t=Pf(t),Pf(e)!==t&&n)throw Error(V(425))}function bo(){}var Zl=null,Jl=null;function eu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var tu=typeof setTimeout=="function"?setTimeout:void 0,R1=typeof clearTimeout=="function"?clearTimeout:void 0,Af=typeof Promise=="function"?Promise:void 0,L1=typeof queueMicrotask=="function"?queueMicrotask:typeof Af<"u"?function(e){return Af.resolve(null).then(e).catch(V1)}:tu;function V1(e){setTimeout(function(){throw e})}function Za(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Ks(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Ks(t)}function Sn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Df(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ns=Math.random().toString(36).slice(2),Lt="__reactFiber$"+ns,Ys="__reactProps$"+ns,tn="__reactContainer$"+ns,nu="__reactEvents$"+ns,I1="__reactListeners$"+ns,F1="__reactHandles$"+ns;function qn(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[tn]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Df(e);e!==null;){if(n=e[Lt])return n;e=Df(e)}return t}e=n,n=e.parentNode}return null}function yi(e){return e=e[Lt]||e[tn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function kr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(V(33))}function oa(e){return e[Ys]||null}var ru=[],Tr=-1;function Dn(e){return{current:e}}function le(e){0>Tr||(e.current=ru[Tr],ru[Tr]=null,Tr--)}function ie(e,t){Tr++,ru[Tr]=e.current,e.current=t}var Nn={},$e=Dn(Nn),Ze=Dn(!1),sr=Nn;function Ur(e,t){var n=e.type.contextTypes;if(!n)return Nn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Je(e){return e=e.childContextTypes,e!=null}function ko(){le(Ze),le($e)}function Mf(e,t,n){if($e.current!==Nn)throw Error(V(168));ie($e,t),ie(Ze,n)}function pg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(V(108,bx(e)||"Unknown",s));return ge({},n,r)}function To(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Nn,sr=$e.current,ie($e,e),ie(Ze,Ze.current),!0}function Rf(e,t,n){var r=e.stateNode;if(!r)throw Error(V(169));n?(e=pg(e,t,sr),r.__reactInternalMemoizedMergedChildContext=e,le(Ze),le($e),ie($e,e)):le(Ze),ie(Ze,n)}var Wt=null,aa=!1,Ja=!1;function gg(e){Wt===null?Wt=[e]:Wt.push(e)}function O1(e){aa=!0,gg(e)}function Mn(){if(!Ja&&Wt!==null){Ja=!0;var e=0,t=ne;try{var n=Wt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Wt=null,aa=!1}catch(s){throw Wt!==null&&(Wt=Wt.slice(e+1)),$p(lc,Mn),s}finally{ne=t,Ja=!1}}return null}var Cr=[],jr=0,Co=null,jo=0,pt=[],gt=0,ir=null,qt=1,Qt="";function zn(e,t){Cr[jr++]=jo,Cr[jr++]=Co,Co=e,jo=t}function yg(e,t,n){pt[gt++]=qt,pt[gt++]=Qt,pt[gt++]=ir,ir=e;var r=qt;e=Qt;var s=32-Pt(r)-1;r&=~(1<<s),n+=1;var i=32-Pt(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,qt=1<<32-Pt(t)+s|n<<s|r,Qt=i+e}else qt=1<<i|n<<s|r,Qt=e}function yc(e){e.return!==null&&(zn(e,1),yg(e,1,0))}function vc(e){for(;e===Co;)Co=Cr[--jr],Cr[jr]=null,jo=Cr[--jr],Cr[jr]=null;for(;e===ir;)ir=pt[--gt],pt[gt]=null,Qt=pt[--gt],pt[gt]=null,qt=pt[--gt],pt[gt]=null}var st=null,rt=null,de=!1,jt=null;function vg(e,t){var n=yt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Lf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,st=e,rt=Sn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,st=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ir!==null?{id:qt,overflow:Qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=yt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,st=e,rt=null,!0):!1;default:return!1}}function su(e){return(e.mode&1)!==0&&(e.flags&128)===0}function iu(e){if(de){var t=rt;if(t){var n=t;if(!Lf(e,t)){if(su(e))throw Error(V(418));t=Sn(n.nextSibling);var r=st;t&&Lf(e,t)?vg(r,n):(e.flags=e.flags&-4097|2,de=!1,st=e)}}else{if(su(e))throw Error(V(418));e.flags=e.flags&-4097|2,de=!1,st=e}}}function Vf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;st=e}function Vi(e){if(e!==st)return!1;if(!de)return Vf(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!eu(e.type,e.memoizedProps)),t&&(t=rt)){if(su(e))throw xg(),Error(V(418));for(;t;)vg(e,t),t=Sn(t.nextSibling)}if(Vf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(V(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){rt=Sn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=st?Sn(e.stateNode.nextSibling):null;return!0}function xg(){for(var e=rt;e;)e=Sn(e.nextSibling)}function Br(){rt=st=null,de=!1}function xc(e){jt===null?jt=[e]:jt.push(e)}var z1=on.ReactCurrentBatchConfig;function ds(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(V(309));var r=n.stateNode}if(!r)throw Error(V(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(V(284));if(!n._owner)throw Error(V(290,e))}return e}function Ii(e,t){throw e=Object.prototype.toString.call(t),Error(V(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function If(e){var t=e._init;return t(e._payload)}function wg(e){function t(p,m){if(e){var y=p.deletions;y===null?(p.deletions=[m],p.flags|=16):y.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function s(p,m){return p=Cn(p,m),p.index=0,p.sibling=null,p}function i(p,m,y){return p.index=y,e?(y=p.alternate,y!==null?(y=y.index,y<m?(p.flags|=2,m):y):(p.flags|=2,m)):(p.flags|=1048576,m)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,m,y,b){return m===null||m.tag!==6?(m=ol(y,p.mode,b),m.return=p,m):(m=s(m,y),m.return=p,m)}function u(p,m,y,b){var C=y.type;return C===xr?d(p,m,y.props.children,b,y.key):m!==null&&(m.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===fn&&If(C)===m.type)?(b=s(m,y.props),b.ref=ds(p,m,y),b.return=p,b):(b=ao(y.type,y.key,y.props,null,p.mode,b),b.ref=ds(p,m,y),b.return=p,b)}function c(p,m,y,b){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=al(y,p.mode,b),m.return=p,m):(m=s(m,y.children||[]),m.return=p,m)}function d(p,m,y,b,C){return m===null||m.tag!==7?(m=er(y,p.mode,b,C),m.return=p,m):(m=s(m,y),m.return=p,m)}function h(p,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=ol(""+m,p.mode,y),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ji:return y=ao(m.type,m.key,m.props,null,p.mode,y),y.ref=ds(p,null,m),y.return=p,y;case vr:return m=al(m,p.mode,y),m.return=p,m;case fn:var b=m._init;return h(p,b(m._payload),y)}if(xs(m)||os(m))return m=er(m,p.mode,y,null),m.return=p,m;Ii(p,m)}return null}function f(p,m,y,b){var C=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return C!==null?null:a(p,m,""+y,b);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ji:return y.key===C?u(p,m,y,b):null;case vr:return y.key===C?c(p,m,y,b):null;case fn:return C=y._init,f(p,m,C(y._payload),b)}if(xs(y)||os(y))return C!==null?null:d(p,m,y,b,null);Ii(p,y)}return null}function g(p,m,y,b,C){if(typeof b=="string"&&b!==""||typeof b=="number")return p=p.get(y)||null,a(m,p,""+b,C);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case ji:return p=p.get(b.key===null?y:b.key)||null,u(m,p,b,C);case vr:return p=p.get(b.key===null?y:b.key)||null,c(m,p,b,C);case fn:var j=b._init;return g(p,m,y,j(b._payload),C)}if(xs(b)||os(b))return p=p.get(y)||null,d(m,p,b,C,null);Ii(m,b)}return null}function v(p,m,y,b){for(var C=null,j=null,_=m,T=m=0,E=null;_!==null&&T<y.length;T++){_.index>T?(E=_,_=null):E=_.sibling;var D=f(p,_,y[T],b);if(D===null){_===null&&(_=E);break}e&&_&&D.alternate===null&&t(p,_),m=i(D,m,T),j===null?C=D:j.sibling=D,j=D,_=E}if(T===y.length)return n(p,_),de&&zn(p,T),C;if(_===null){for(;T<y.length;T++)_=h(p,y[T],b),_!==null&&(m=i(_,m,T),j===null?C=_:j.sibling=_,j=_);return de&&zn(p,T),C}for(_=r(p,_);T<y.length;T++)E=g(_,p,T,y[T],b),E!==null&&(e&&E.alternate!==null&&_.delete(E.key===null?T:E.key),m=i(E,m,T),j===null?C=E:j.sibling=E,j=E);return e&&_.forEach(function(I){return t(p,I)}),de&&zn(p,T),C}function x(p,m,y,b){var C=os(y);if(typeof C!="function")throw Error(V(150));if(y=C.call(y),y==null)throw Error(V(151));for(var j=C=null,_=m,T=m=0,E=null,D=y.next();_!==null&&!D.done;T++,D=y.next()){_.index>T?(E=_,_=null):E=_.sibling;var I=f(p,_,D.value,b);if(I===null){_===null&&(_=E);break}e&&_&&I.alternate===null&&t(p,_),m=i(I,m,T),j===null?C=I:j.sibling=I,j=I,_=E}if(D.done)return n(p,_),de&&zn(p,T),C;if(_===null){for(;!D.done;T++,D=y.next())D=h(p,D.value,b),D!==null&&(m=i(D,m,T),j===null?C=D:j.sibling=D,j=D);return de&&zn(p,T),C}for(_=r(p,_);!D.done;T++,D=y.next())D=g(_,p,T,D.value,b),D!==null&&(e&&D.alternate!==null&&_.delete(D.key===null?T:D.key),m=i(D,m,T),j===null?C=D:j.sibling=D,j=D);return e&&_.forEach(function(A){return t(p,A)}),de&&zn(p,T),C}function S(p,m,y,b){if(typeof y=="object"&&y!==null&&y.type===xr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ji:e:{for(var C=y.key,j=m;j!==null;){if(j.key===C){if(C=y.type,C===xr){if(j.tag===7){n(p,j.sibling),m=s(j,y.props.children),m.return=p,p=m;break e}}else if(j.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===fn&&If(C)===j.type){n(p,j.sibling),m=s(j,y.props),m.ref=ds(p,j,y),m.return=p,p=m;break e}n(p,j);break}else t(p,j);j=j.sibling}y.type===xr?(m=er(y.props.children,p.mode,b,y.key),m.return=p,p=m):(b=ao(y.type,y.key,y.props,null,p.mode,b),b.ref=ds(p,m,y),b.return=p,p=b)}return o(p);case vr:e:{for(j=y.key;m!==null;){if(m.key===j)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(p,m.sibling),m=s(m,y.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=al(y,p.mode,b),m.return=p,p=m}return o(p);case fn:return j=y._init,S(p,m,j(y._payload),b)}if(xs(y))return v(p,m,y,b);if(os(y))return x(p,m,y,b);Ii(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(p,m.sibling),m=s(m,y),m.return=p,p=m):(n(p,m),m=ol(y,p.mode,b),m.return=p,p=m),o(p)):n(p,m)}return S}var Hr=wg(!0),Sg=wg(!1),No=Dn(null),_o=null,Nr=null,wc=null;function Sc(){wc=Nr=_o=null}function bc(e){var t=No.current;le(No),e._currentValue=t}function ou(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Or(e,t){_o=e,wc=Nr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ye=!0),e.firstContext=null)}function xt(e){var t=e._currentValue;if(wc!==e)if(e={context:e,memoizedValue:t,next:null},Nr===null){if(_o===null)throw Error(V(308));Nr=e,_o.dependencies={lanes:0,firstContext:e}}else Nr=Nr.next=e;return t}var Qn=null;function kc(e){Qn===null?Qn=[e]:Qn.push(e)}function bg(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,kc(t)):(n.next=s.next,s.next=n),t.interleaved=n,nn(e,r)}function nn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var hn=!1;function Tc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Xt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ee&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,nn(e,n)}return s=r.interleaved,s===null?(t.next=t,kc(r)):(t.next=s.next,s.next=t),r.interleaved=t,nn(e,n)}function to(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uc(e,n)}}function Ff(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Eo(e,t,n,r){var s=e.updateQueue;hn=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=u))}if(i!==null){var h=s.baseState;o=0,d=c=u=null,a=i;do{var f=a.lane,g=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){h=v.call(g,h,f);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(g,h,f):v,f==null)break e;h=ge({},h,f);break e;case 2:hn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else g={eventTime:g,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=g,u=h):d=d.next=g,o|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(u=h),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);ar|=o,e.lanes=o,e.memoizedState=h}}function Of(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(V(191,s));s.call(r)}}}var vi={},Ft=Dn(vi),Zs=Dn(vi),Js=Dn(vi);function Xn(e){if(e===vi)throw Error(V(174));return e}function Cc(e,t){switch(ie(Js,t),ie(Zs,e),ie(Ft,vi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:zl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=zl(t,e)}le(Ft),ie(Ft,t)}function Wr(){le(Ft),le(Zs),le(Js)}function Tg(e){Xn(Js.current);var t=Xn(Ft.current),n=zl(t,e.type);t!==n&&(ie(Zs,e),ie(Ft,n))}function jc(e){Zs.current===e&&(le(Ft),le(Zs))}var he=Dn(0);function Po(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var el=[];function Nc(){for(var e=0;e<el.length;e++)el[e]._workInProgressVersionPrimary=null;el.length=0}var no=on.ReactCurrentDispatcher,tl=on.ReactCurrentBatchConfig,or=0,pe=null,Ne=null,Ee=null,Ao=!1,As=!1,ei=0,$1=0;function Ve(){throw Error(V(321))}function _c(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Dt(e[n],t[n]))return!1;return!0}function Ec(e,t,n,r,s,i){if(or=i,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,no.current=e===null||e.memoizedState===null?W1:K1,e=n(r,s),As){i=0;do{if(As=!1,ei=0,25<=i)throw Error(V(301));i+=1,Ee=Ne=null,t.updateQueue=null,no.current=G1,e=n(r,s)}while(As)}if(no.current=Do,t=Ne!==null&&Ne.next!==null,or=0,Ee=Ne=pe=null,Ao=!1,t)throw Error(V(300));return e}function Pc(){var e=ei!==0;return ei=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function wt(){if(Ne===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ne.next;var t=Ee===null?pe.memoizedState:Ee.next;if(t!==null)Ee=t,Ne=e;else{if(e===null)throw Error(V(310));Ne=e,e={memoizedState:Ne.memoizedState,baseState:Ne.baseState,baseQueue:Ne.baseQueue,queue:Ne.queue,next:null},Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function ti(e,t){return typeof t=="function"?t(e):t}function nl(e){var t=wt(),n=t.queue;if(n===null)throw Error(V(311));n.lastRenderedReducer=e;var r=Ne,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,u=null,c=i;do{var d=c.lane;if((or&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=h,o=r):u=u.next=h,pe.lanes|=d,ar|=d}c=c.next}while(c!==null&&c!==i);u===null?o=r:u.next=a,Dt(r,t.memoizedState)||(Ye=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,pe.lanes|=i,ar|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function rl(e){var t=wt(),n=t.queue;if(n===null)throw Error(V(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Dt(i,t.memoizedState)||(Ye=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Cg(){}function jg(e,t){var n=pe,r=wt(),s=t(),i=!Dt(r.memoizedState,s);if(i&&(r.memoizedState=s,Ye=!0),r=r.queue,Ac(Eg.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,ni(9,_g.bind(null,n,r,s,t),void 0,null),Pe===null)throw Error(V(349));or&30||Ng(n,t,s)}return s}function Ng(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function _g(e,t,n,r){t.value=n,t.getSnapshot=r,Pg(t)&&Ag(e)}function Eg(e,t,n){return n(function(){Pg(t)&&Ag(e)})}function Pg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Dt(e,n)}catch{return!0}}function Ag(e){var t=nn(e,1);t!==null&&At(t,e,1,-1)}function zf(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ti,lastRenderedState:e},t.queue=e,e=e.dispatch=H1.bind(null,pe,e),[t.memoizedState,e]}function ni(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Dg(){return wt().memoizedState}function ro(e,t,n,r){var s=Rt();pe.flags|=e,s.memoizedState=ni(1|t,n,void 0,r===void 0?null:r)}function la(e,t,n,r){var s=wt();r=r===void 0?null:r;var i=void 0;if(Ne!==null){var o=Ne.memoizedState;if(i=o.destroy,r!==null&&_c(r,o.deps)){s.memoizedState=ni(t,n,i,r);return}}pe.flags|=e,s.memoizedState=ni(1|t,n,i,r)}function $f(e,t){return ro(8390656,8,e,t)}function Ac(e,t){return la(2048,8,e,t)}function Mg(e,t){return la(4,2,e,t)}function Rg(e,t){return la(4,4,e,t)}function Lg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vg(e,t,n){return n=n!=null?n.concat([e]):null,la(4,4,Lg.bind(null,t,e),n)}function Dc(){}function Ig(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&_c(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Fg(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&_c(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Og(e,t,n){return or&21?(Dt(n,t)||(n=Hp(),pe.lanes|=n,ar|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ye=!0),e.memoizedState=n)}function U1(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=tl.transition;tl.transition={};try{e(!1),t()}finally{ne=n,tl.transition=r}}function zg(){return wt().memoizedState}function B1(e,t,n){var r=Tn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},$g(e))Ug(t,n);else if(n=bg(e,t,n,r),n!==null){var s=Ke();At(n,e,r,s),Bg(n,t,r)}}function H1(e,t,n){var r=Tn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if($g(e))Ug(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,Dt(a,o)){var u=t.interleaved;u===null?(s.next=s,kc(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=bg(e,t,s,r),n!==null&&(s=Ke(),At(n,e,r,s),Bg(n,t,r))}}function $g(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Ug(e,t){As=Ao=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uc(e,n)}}var Do={readContext:xt,useCallback:Ve,useContext:Ve,useEffect:Ve,useImperativeHandle:Ve,useInsertionEffect:Ve,useLayoutEffect:Ve,useMemo:Ve,useReducer:Ve,useRef:Ve,useState:Ve,useDebugValue:Ve,useDeferredValue:Ve,useTransition:Ve,useMutableSource:Ve,useSyncExternalStore:Ve,useId:Ve,unstable_isNewReconciler:!1},W1={readContext:xt,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:$f,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ro(4194308,4,Lg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ro(4194308,4,e,t)},useInsertionEffect:function(e,t){return ro(4,2,e,t)},useMemo:function(e,t){var n=Rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=B1.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:zf,useDebugValue:Dc,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=zf(!1),t=e[0];return e=U1.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,s=Rt();if(de){if(n===void 0)throw Error(V(407));n=n()}else{if(n=t(),Pe===null)throw Error(V(349));or&30||Ng(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,$f(Eg.bind(null,r,i,e),[e]),r.flags|=2048,ni(9,_g.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Rt(),t=Pe.identifierPrefix;if(de){var n=Qt,r=qt;n=(r&~(1<<32-Pt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ei++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=$1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},K1={readContext:xt,useCallback:Ig,useContext:xt,useEffect:Ac,useImperativeHandle:Vg,useInsertionEffect:Mg,useLayoutEffect:Rg,useMemo:Fg,useReducer:nl,useRef:Dg,useState:function(){return nl(ti)},useDebugValue:Dc,useDeferredValue:function(e){var t=wt();return Og(t,Ne.memoizedState,e)},useTransition:function(){var e=nl(ti)[0],t=wt().memoizedState;return[e,t]},useMutableSource:Cg,useSyncExternalStore:jg,useId:zg,unstable_isNewReconciler:!1},G1={readContext:xt,useCallback:Ig,useContext:xt,useEffect:Ac,useImperativeHandle:Vg,useInsertionEffect:Mg,useLayoutEffect:Rg,useMemo:Fg,useReducer:rl,useRef:Dg,useState:function(){return rl(ti)},useDebugValue:Dc,useDeferredValue:function(e){var t=wt();return Ne===null?t.memoizedState=e:Og(t,Ne.memoizedState,e)},useTransition:function(){var e=rl(ti)[0],t=wt().memoizedState;return[e,t]},useMutableSource:Cg,useSyncExternalStore:jg,useId:zg,unstable_isNewReconciler:!1};function Tt(e,t){if(e&&e.defaultProps){t=ge({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function au(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ge({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ua={isMounted:function(e){return(e=e._reactInternals)?fr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ke(),s=Tn(e),i=Xt(r,s);i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,s),t!==null&&(At(t,e,s,r),to(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ke(),s=Tn(e),i=Xt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,s),t!==null&&(At(t,e,s,r),to(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ke(),r=Tn(e),s=Xt(n,r);s.tag=2,t!=null&&(s.callback=t),t=bn(e,s,r),t!==null&&(At(t,e,r,n),to(t,e,r))}};function Uf(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!qs(n,r)||!qs(s,i):!0}function Hg(e,t,n){var r=!1,s=Nn,i=t.contextType;return typeof i=="object"&&i!==null?i=xt(i):(s=Je(t)?sr:$e.current,r=t.contextTypes,i=(r=r!=null)?Ur(e,s):Nn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ua,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Bf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ua.enqueueReplaceState(t,t.state,null)}function lu(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Tc(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=xt(i):(i=Je(t)?sr:$e.current,s.context=Ur(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(au(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&ua.enqueueReplaceState(s,s.state,null),Eo(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Kr(e,t){try{var n="",r=t;do n+=Sx(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function sl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function uu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var q1=typeof WeakMap=="function"?WeakMap:Map;function Wg(e,t,n){n=Xt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ro||(Ro=!0,xu=r),uu(e,t)},n}function Kg(e,t,n){n=Xt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){uu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){uu(e,t),typeof r!="function"&&(kn===null?kn=new Set([this]):kn.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Hf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new q1;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=lw.bind(null,e,t,n),t.then(e,e))}function Wf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Kf(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Xt(-1,1),t.tag=2,bn(n,t,1))),n.lanes|=1),e)}var Q1=on.ReactCurrentOwner,Ye=!1;function Ue(e,t,n,r){t.child=e===null?Sg(t,null,n,r):Hr(t,e.child,n,r)}function Gf(e,t,n,r,s){n=n.render;var i=t.ref;return Or(t,s),r=Ec(e,t,n,r,i,s),n=Pc(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(de&&n&&yc(t),t.flags|=1,Ue(e,t,r,s),t.child)}function qf(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!zc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Gg(e,t,i,r,s)):(e=ao(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:qs,n(o,r)&&e.ref===t.ref)return rn(e,t,s)}return t.flags|=1,e=Cn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Gg(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(qs(i,r)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(Ye=!0);else return t.lanes=e.lanes,rn(e,t,s)}return cu(e,t,n,r,s)}function qg(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ie(Er,tt),tt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ie(Er,tt),tt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ie(Er,tt),tt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ie(Er,tt),tt|=r;return Ue(e,t,s,n),t.child}function Qg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function cu(e,t,n,r,s){var i=Je(n)?sr:$e.current;return i=Ur(t,i),Or(t,s),n=Ec(e,t,n,r,i,s),r=Pc(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(de&&r&&yc(t),t.flags|=1,Ue(e,t,n,s),t.child)}function Qf(e,t,n,r,s){if(Je(n)){var i=!0;To(t)}else i=!1;if(Or(t,s),t.stateNode===null)so(e,t),Hg(t,n,r),lu(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=xt(c):(c=Je(n)?sr:$e.current,c=Ur(t,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||u!==c)&&Bf(t,o,r,c),hn=!1;var f=t.memoizedState;o.state=f,Eo(t,r,o,s),u=t.memoizedState,a!==r||f!==u||Ze.current||hn?(typeof d=="function"&&(au(t,n,d,r),u=t.memoizedState),(a=hn||Uf(t,n,a,r,f,u,c))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,kg(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Tt(t.type,a),o.props=c,h=t.pendingProps,f=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=xt(u):(u=Je(n)?sr:$e.current,u=Ur(t,u));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==u)&&Bf(t,o,r,u),hn=!1,f=t.memoizedState,o.state=f,Eo(t,r,o,s);var v=t.memoizedState;a!==h||f!==v||Ze.current||hn?(typeof g=="function"&&(au(t,n,g,r),v=t.memoizedState),(c=hn||Uf(t,n,c,r,f,v,u)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return du(e,t,n,r,i,s)}function du(e,t,n,r,s,i){Qg(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Rf(t,n,!1),rn(e,t,i);r=t.stateNode,Q1.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Hr(t,e.child,null,i),t.child=Hr(t,null,a,i)):Ue(e,t,a,i),t.memoizedState=r.state,s&&Rf(t,n,!0),t.child}function Xg(e){var t=e.stateNode;t.pendingContext?Mf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mf(e,t.context,!1),Cc(e,t.containerInfo)}function Xf(e,t,n,r,s){return Br(),xc(s),t.flags|=256,Ue(e,t,n,r),t.child}var fu={dehydrated:null,treeContext:null,retryLane:0};function hu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Yg(e,t,n){var r=t.pendingProps,s=he.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ie(he,s&1),e===null)return iu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=fa(o,r,0,null),e=er(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=hu(n),t.memoizedState=fu,e):Mc(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return X1(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Cn(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Cn(a,i):(i=er(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?hu(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=fu,r}return i=e.child,e=i.sibling,r=Cn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Mc(e,t){return t=fa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fi(e,t,n,r){return r!==null&&xc(r),Hr(t,e.child,null,n),e=Mc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function X1(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=sl(Error(V(422))),Fi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=fa({mode:"visible",children:r.children},s,0,null),i=er(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Hr(t,e.child,null,o),t.child.memoizedState=hu(o),t.memoizedState=fu,i);if(!(t.mode&1))return Fi(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(V(419)),r=sl(i,r,void 0),Fi(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ye||a){if(r=Pe,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,nn(e,s),At(r,e,s,-1))}return Oc(),r=sl(Error(V(421))),Fi(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=uw.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,rt=Sn(s.nextSibling),st=t,de=!0,jt=null,e!==null&&(pt[gt++]=qt,pt[gt++]=Qt,pt[gt++]=ir,qt=e.id,Qt=e.overflow,ir=t),t=Mc(t,r.children),t.flags|=4096,t)}function Yf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ou(e.return,t,n)}function il(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Zg(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(Ue(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Yf(e,n,t);else if(e.tag===19)Yf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ie(he,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Po(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),il(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Po(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}il(t,!0,n,null,i);break;case"together":il(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function so(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function rn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ar|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(V(153));if(t.child!==null){for(e=t.child,n=Cn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Cn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Y1(e,t,n){switch(t.tag){case 3:Xg(t),Br();break;case 5:Tg(t);break;case 1:Je(t.type)&&To(t);break;case 4:Cc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;ie(No,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ie(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Yg(e,t,n):(ie(he,he.current&1),e=rn(e,t,n),e!==null?e.sibling:null);ie(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Zg(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ie(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,qg(e,t,n)}return rn(e,t,n)}var Jg,mu,e0,t0;Jg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};mu=function(){};e0=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Xn(Ft.current);var i=null;switch(n){case"input":s=Vl(e,s),r=Vl(e,r),i=[];break;case"select":s=ge({},s,{value:void 0}),r=ge({},r,{value:void 0}),i=[];break;case"textarea":s=Ol(e,s),r=Ol(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=bo)}$l(n,r);var o;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&($s.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&($s.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&oe("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};t0=function(e,t,n,r){n!==r&&(t.flags|=4)};function fs(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Z1(e,t,n){var r=t.pendingProps;switch(vc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ie(t),null;case 1:return Je(t.type)&&ko(),Ie(t),null;case 3:return r=t.stateNode,Wr(),le(Ze),le($e),Nc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Vi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,jt!==null&&(bu(jt),jt=null))),mu(e,t),Ie(t),null;case 5:jc(t);var s=Xn(Js.current);if(n=t.type,e!==null&&t.stateNode!=null)e0(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(V(166));return Ie(t),null}if(e=Xn(Ft.current),Vi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Lt]=t,r[Ys]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(s=0;s<Ss.length;s++)oe(Ss[s],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":of(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":lf(r,i),oe("invalid",r)}$l(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,a,e),s=["children",""+a]):$s.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&oe("scroll",r)}switch(n){case"input":Ni(r),af(r,i,!0);break;case"textarea":Ni(r),uf(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=bo)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ep(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Lt]=t,e[Ys]=r,Jg(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ul(n,r),n){case"dialog":oe("cancel",e),oe("close",e),s=r;break;case"iframe":case"object":case"embed":oe("load",e),s=r;break;case"video":case"audio":for(s=0;s<Ss.length;s++)oe(Ss[s],e);s=r;break;case"source":oe("error",e),s=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),s=r;break;case"details":oe("toggle",e),s=r;break;case"input":of(e,r),s=Vl(e,r),oe("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=ge({},r,{value:void 0}),oe("invalid",e);break;case"textarea":lf(e,r),s=Ol(e,r),oe("invalid",e);break;default:s=r}$l(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?Dp(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Pp(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Us(e,u):typeof u=="number"&&Us(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&($s.hasOwnProperty(i)?u!=null&&i==="onScroll"&&oe("scroll",e):u!=null&&rc(e,i,u,o))}switch(n){case"input":Ni(e),af(e,r,!1);break;case"textarea":Ni(e),uf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+jn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Lr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Lr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=bo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ie(t),null;case 6:if(e&&t.stateNode!=null)t0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(V(166));if(n=Xn(Js.current),Xn(Ft.current),Vi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Lt]=t,(i=r.nodeValue!==n)&&(e=st,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Lt]=t,t.stateNode=r}return Ie(t),null;case 13:if(le(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&rt!==null&&t.mode&1&&!(t.flags&128))xg(),Br(),t.flags|=98560,i=!1;else if(i=Vi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(V(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(V(317));i[Lt]=t}else Br(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ie(t),i=!1}else jt!==null&&(bu(jt),jt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?_e===0&&(_e=3):Oc())),t.updateQueue!==null&&(t.flags|=4),Ie(t),null);case 4:return Wr(),mu(e,t),e===null&&Qs(t.stateNode.containerInfo),Ie(t),null;case 10:return bc(t.type._context),Ie(t),null;case 17:return Je(t.type)&&ko(),Ie(t),null;case 19:if(le(he),i=t.memoizedState,i===null)return Ie(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)fs(i,!1);else{if(_e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Po(e),o!==null){for(t.flags|=128,fs(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ie(he,he.current&1|2),t.child}e=e.sibling}i.tail!==null&&Se()>Gr&&(t.flags|=128,r=!0,fs(i,!1),t.lanes=4194304)}else{if(!r)if(e=Po(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),fs(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!de)return Ie(t),null}else 2*Se()-i.renderingStartTime>Gr&&n!==1073741824&&(t.flags|=128,r=!0,fs(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Se(),t.sibling=null,n=he.current,ie(he,r?n&1|2:n&1),t):(Ie(t),null);case 22:case 23:return Fc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?tt&1073741824&&(Ie(t),t.subtreeFlags&6&&(t.flags|=8192)):Ie(t),null;case 24:return null;case 25:return null}throw Error(V(156,t.tag))}function J1(e,t){switch(vc(t),t.tag){case 1:return Je(t.type)&&ko(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Wr(),le(Ze),le($e),Nc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return jc(t),null;case 13:if(le(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(V(340));Br()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(he),null;case 4:return Wr(),null;case 10:return bc(t.type._context),null;case 22:case 23:return Fc(),null;case 24:return null;default:return null}}var Oi=!1,Oe=!1,ew=typeof WeakSet=="function"?WeakSet:Set,B=null;function _r(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function pu(e,t,n){try{n()}catch(r){ve(e,t,r)}}var Zf=!1;function tw(e,t){if(Zl=xo,e=og(),gc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,u=-1,c=0,d=0,h=e,f=null;t:for(;;){for(var g;h!==n||s!==0&&h.nodeType!==3||(a=o+s),h!==i||r!==0&&h.nodeType!==3||(u=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)f=h,h=g;for(;;){if(h===e)break t;if(f===n&&++c===s&&(a=o),f===i&&++d===r&&(u=o),(g=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=g}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Jl={focusedElem:e,selectionRange:n},xo=!1,B=t;B!==null;)if(t=B,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,B=e;else for(;B!==null;){t=B;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?x:Tt(t.type,x),S);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(V(163))}}catch(b){ve(t,t.return,b)}if(e=t.sibling,e!==null){e.return=t.return,B=e;break}B=t.return}return v=Zf,Zf=!1,v}function Ds(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&pu(t,n,i)}s=s.next}while(s!==r)}}function ca(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function gu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function n0(e){var t=e.alternate;t!==null&&(e.alternate=null,n0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lt],delete t[Ys],delete t[nu],delete t[I1],delete t[F1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function r0(e){return e.tag===5||e.tag===3||e.tag===4}function Jf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||r0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function yu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bo));else if(r!==4&&(e=e.child,e!==null))for(yu(e,t,n),e=e.sibling;e!==null;)yu(e,t,n),e=e.sibling}function vu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(vu(e,t,n),e=e.sibling;e!==null;)vu(e,t,n),e=e.sibling}var Ae=null,Ct=!1;function un(e,t,n){for(n=n.child;n!==null;)s0(e,t,n),n=n.sibling}function s0(e,t,n){if(It&&typeof It.onCommitFiberUnmount=="function")try{It.onCommitFiberUnmount(na,n)}catch{}switch(n.tag){case 5:Oe||_r(n,t);case 6:var r=Ae,s=Ct;Ae=null,un(e,t,n),Ae=r,Ct=s,Ae!==null&&(Ct?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(Ct?(e=Ae,n=n.stateNode,e.nodeType===8?Za(e.parentNode,n):e.nodeType===1&&Za(e,n),Ks(e)):Za(Ae,n.stateNode));break;case 4:r=Ae,s=Ct,Ae=n.stateNode.containerInfo,Ct=!0,un(e,t,n),Ae=r,Ct=s;break;case 0:case 11:case 14:case 15:if(!Oe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&pu(n,t,o),s=s.next}while(s!==r)}un(e,t,n);break;case 1:if(!Oe&&(_r(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ve(n,t,a)}un(e,t,n);break;case 21:un(e,t,n);break;case 22:n.mode&1?(Oe=(r=Oe)||n.memoizedState!==null,un(e,t,n),Oe=r):un(e,t,n);break;default:un(e,t,n)}}function eh(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new ew),t.forEach(function(r){var s=cw.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:Ae=a.stateNode,Ct=!1;break e;case 3:Ae=a.stateNode.containerInfo,Ct=!0;break e;case 4:Ae=a.stateNode.containerInfo,Ct=!0;break e}a=a.return}if(Ae===null)throw Error(V(160));s0(i,o,s),Ae=null,Ct=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){ve(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)i0(t,e),t=t.sibling}function i0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(bt(t,e),Mt(e),r&4){try{Ds(3,e,e.return),ca(3,e)}catch(x){ve(e,e.return,x)}try{Ds(5,e,e.return)}catch(x){ve(e,e.return,x)}}break;case 1:bt(t,e),Mt(e),r&512&&n!==null&&_r(n,n.return);break;case 5:if(bt(t,e),Mt(e),r&512&&n!==null&&_r(n,n.return),e.flags&32){var s=e.stateNode;try{Us(s,"")}catch(x){ve(e,e.return,x)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Np(s,i),Ul(a,o);var c=Ul(a,i);for(o=0;o<u.length;o+=2){var d=u[o],h=u[o+1];d==="style"?Dp(s,h):d==="dangerouslySetInnerHTML"?Pp(s,h):d==="children"?Us(s,h):rc(s,d,h,c)}switch(a){case"input":Il(s,i);break;case"textarea":_p(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Lr(s,!!i.multiple,g,!1):f!==!!i.multiple&&(i.defaultValue!=null?Lr(s,!!i.multiple,i.defaultValue,!0):Lr(s,!!i.multiple,i.multiple?[]:"",!1))}s[Ys]=i}catch(x){ve(e,e.return,x)}}break;case 6:if(bt(t,e),Mt(e),r&4){if(e.stateNode===null)throw Error(V(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(x){ve(e,e.return,x)}}break;case 3:if(bt(t,e),Mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ks(t.containerInfo)}catch(x){ve(e,e.return,x)}break;case 4:bt(t,e),Mt(e);break;case 13:bt(t,e),Mt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Vc=Se())),r&4&&eh(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Oe=(c=Oe)||d,bt(t,e),Oe=c):bt(t,e),Mt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(B=e,d=e.child;d!==null;){for(h=B=d;B!==null;){switch(f=B,g=f.child,f.tag){case 0:case 11:case 14:case 15:Ds(4,f,f.return);break;case 1:_r(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){ve(r,n,x)}}break;case 5:_r(f,f.return);break;case 22:if(f.memoizedState!==null){nh(h);continue}}g!==null?(g.return=f,B=g):nh(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,c?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=h.stateNode,u=h.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Ap("display",o))}catch(x){ve(e,e.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(x){ve(e,e.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:bt(t,e),Mt(e),r&4&&eh(e);break;case 21:break;default:bt(t,e),Mt(e)}}function Mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(r0(n)){var r=n;break e}n=n.return}throw Error(V(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Us(s,""),r.flags&=-33);var i=Jf(e);vu(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Jf(e);yu(e,a,o);break;default:throw Error(V(161))}}catch(u){ve(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function nw(e,t,n){B=e,o0(e)}function o0(e,t,n){for(var r=(e.mode&1)!==0;B!==null;){var s=B,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Oi;if(!o){var a=s.alternate,u=a!==null&&a.memoizedState!==null||Oe;a=Oi;var c=Oe;if(Oi=o,(Oe=u)&&!c)for(B=s;B!==null;)o=B,u=o.child,o.tag===22&&o.memoizedState!==null?rh(s):u!==null?(u.return=o,B=u):rh(s);for(;i!==null;)B=i,o0(i),i=i.sibling;B=s,Oi=a,Oe=c}th(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,B=i):th(e)}}function th(e){for(;B!==null;){var t=B;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Oe||ca(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Oe)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Tt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Of(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Of(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Ks(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(V(163))}Oe||t.flags&512&&gu(t)}catch(f){ve(t,t.return,f)}}if(t===e){B=null;break}if(n=t.sibling,n!==null){n.return=t.return,B=n;break}B=t.return}}function nh(e){for(;B!==null;){var t=B;if(t===e){B=null;break}var n=t.sibling;if(n!==null){n.return=t.return,B=n;break}B=t.return}}function rh(e){for(;B!==null;){var t=B;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ca(4,t)}catch(u){ve(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){ve(t,s,u)}}var i=t.return;try{gu(t)}catch(u){ve(t,i,u)}break;case 5:var o=t.return;try{gu(t)}catch(u){ve(t,o,u)}}}catch(u){ve(t,t.return,u)}if(t===e){B=null;break}var a=t.sibling;if(a!==null){a.return=t.return,B=a;break}B=t.return}}var rw=Math.ceil,Mo=on.ReactCurrentDispatcher,Rc=on.ReactCurrentOwner,vt=on.ReactCurrentBatchConfig,ee=0,Pe=null,Ce=null,Me=0,tt=0,Er=Dn(0),_e=0,ri=null,ar=0,da=0,Lc=0,Ms=null,Xe=null,Vc=0,Gr=1/0,Ht=null,Ro=!1,xu=null,kn=null,zi=!1,yn=null,Lo=0,Rs=0,wu=null,io=-1,oo=0;function Ke(){return ee&6?Se():io!==-1?io:io=Se()}function Tn(e){return e.mode&1?ee&2&&Me!==0?Me&-Me:z1.transition!==null?(oo===0&&(oo=Hp()),oo):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Yp(e.type)),e):1}function At(e,t,n,r){if(50<Rs)throw Rs=0,wu=null,Error(V(185));pi(e,n,r),(!(ee&2)||e!==Pe)&&(e===Pe&&(!(ee&2)&&(da|=n),_e===4&&pn(e,Me)),et(e,r),n===1&&ee===0&&!(t.mode&1)&&(Gr=Se()+500,aa&&Mn()))}function et(e,t){var n=e.callbackNode;zx(e,t);var r=vo(e,e===Pe?Me:0);if(r===0)n!==null&&ff(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ff(n),t===1)e.tag===0?O1(sh.bind(null,e)):gg(sh.bind(null,e)),L1(function(){!(ee&6)&&Mn()}),n=null;else{switch(Wp(r)){case 1:n=lc;break;case 4:n=Up;break;case 16:n=yo;break;case 536870912:n=Bp;break;default:n=yo}n=m0(n,a0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function a0(e,t){if(io=-1,oo=0,ee&6)throw Error(V(327));var n=e.callbackNode;if(zr()&&e.callbackNode!==n)return null;var r=vo(e,e===Pe?Me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Vo(e,r);else{t=r;var s=ee;ee|=2;var i=u0();(Pe!==e||Me!==t)&&(Ht=null,Gr=Se()+500,Jn(e,t));do try{ow();break}catch(a){l0(e,a)}while(!0);Sc(),Mo.current=i,ee=s,Ce!==null?t=0:(Pe=null,Me=0,t=_e)}if(t!==0){if(t===2&&(s=Gl(e),s!==0&&(r=s,t=Su(e,s))),t===1)throw n=ri,Jn(e,0),pn(e,r),et(e,Se()),n;if(t===6)pn(e,r);else{if(s=e.current.alternate,!(r&30)&&!sw(s)&&(t=Vo(e,r),t===2&&(i=Gl(e),i!==0&&(r=i,t=Su(e,i))),t===1))throw n=ri,Jn(e,0),pn(e,r),et(e,Se()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(V(345));case 2:$n(e,Xe,Ht);break;case 3:if(pn(e,r),(r&130023424)===r&&(t=Vc+500-Se(),10<t)){if(vo(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Ke(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=tu($n.bind(null,e,Xe,Ht),t);break}$n(e,Xe,Ht);break;case 4:if(pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Pt(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*rw(r/1960))-r,10<r){e.timeoutHandle=tu($n.bind(null,e,Xe,Ht),r);break}$n(e,Xe,Ht);break;case 5:$n(e,Xe,Ht);break;default:throw Error(V(329))}}}return et(e,Se()),e.callbackNode===n?a0.bind(null,e):null}function Su(e,t){var n=Ms;return e.current.memoizedState.isDehydrated&&(Jn(e,t).flags|=256),e=Vo(e,t),e!==2&&(t=Xe,Xe=n,t!==null&&bu(t)),e}function bu(e){Xe===null?Xe=e:Xe.push.apply(Xe,e)}function sw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Dt(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pn(e,t){for(t&=~Lc,t&=~da,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Pt(t),r=1<<n;e[n]=-1,t&=~r}}function sh(e){if(ee&6)throw Error(V(327));zr();var t=vo(e,0);if(!(t&1))return et(e,Se()),null;var n=Vo(e,t);if(e.tag!==0&&n===2){var r=Gl(e);r!==0&&(t=r,n=Su(e,r))}if(n===1)throw n=ri,Jn(e,0),pn(e,t),et(e,Se()),n;if(n===6)throw Error(V(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$n(e,Xe,Ht),et(e,Se()),null}function Ic(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(Gr=Se()+500,aa&&Mn())}}function lr(e){yn!==null&&yn.tag===0&&!(ee&6)&&zr();var t=ee;ee|=1;var n=vt.transition,r=ne;try{if(vt.transition=null,ne=1,e)return e()}finally{ne=r,vt.transition=n,ee=t,!(ee&6)&&Mn()}}function Fc(){tt=Er.current,le(Er)}function Jn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,R1(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(vc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ko();break;case 3:Wr(),le(Ze),le($e),Nc();break;case 5:jc(r);break;case 4:Wr();break;case 13:le(he);break;case 19:le(he);break;case 10:bc(r.type._context);break;case 22:case 23:Fc()}n=n.return}if(Pe=e,Ce=e=Cn(e.current,null),Me=tt=t,_e=0,ri=null,Lc=da=ar=0,Xe=Ms=null,Qn!==null){for(t=0;t<Qn.length;t++)if(n=Qn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}Qn=null}return e}function l0(e,t){do{var n=Ce;try{if(Sc(),no.current=Do,Ao){for(var r=pe.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ao=!1}if(or=0,Ee=Ne=pe=null,As=!1,ei=0,Rc.current=null,n===null||n.return===null){_e=1,ri=t,Ce=null;break}e:{var i=e,o=n.return,a=n,u=t;if(t=Me,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=Wf(o);if(g!==null){g.flags&=-257,Kf(g,o,a,i,t),g.mode&1&&Hf(i,c,t),t=g,u=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(u),t.updateQueue=x}else v.add(u);break e}else{if(!(t&1)){Hf(i,c,t),Oc();break e}u=Error(V(426))}}else if(de&&a.mode&1){var S=Wf(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Kf(S,o,a,i,t),xc(Kr(u,a));break e}}i=u=Kr(u,a),_e!==4&&(_e=2),Ms===null?Ms=[i]:Ms.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Wg(i,u,t);Ff(i,p);break e;case 1:a=u;var m=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(kn===null||!kn.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var b=Kg(i,a,t);Ff(i,b);break e}}i=i.return}while(i!==null)}d0(n)}catch(C){t=C,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function u0(){var e=Mo.current;return Mo.current=Do,e===null?Do:e}function Oc(){(_e===0||_e===3||_e===2)&&(_e=4),Pe===null||!(ar&268435455)&&!(da&268435455)||pn(Pe,Me)}function Vo(e,t){var n=ee;ee|=2;var r=u0();(Pe!==e||Me!==t)&&(Ht=null,Jn(e,t));do try{iw();break}catch(s){l0(e,s)}while(!0);if(Sc(),ee=n,Mo.current=r,Ce!==null)throw Error(V(261));return Pe=null,Me=0,_e}function iw(){for(;Ce!==null;)c0(Ce)}function ow(){for(;Ce!==null&&!Ax();)c0(Ce)}function c0(e){var t=h0(e.alternate,e,tt);e.memoizedProps=e.pendingProps,t===null?d0(e):Ce=t,Rc.current=null}function d0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=J1(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,Ce=null;return}}else if(n=Z1(n,t,tt),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);_e===0&&(_e=5)}function $n(e,t,n){var r=ne,s=vt.transition;try{vt.transition=null,ne=1,aw(e,t,n,r)}finally{vt.transition=s,ne=r}return null}function aw(e,t,n,r){do zr();while(yn!==null);if(ee&6)throw Error(V(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(V(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if($x(e,i),e===Pe&&(Ce=Pe=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||zi||(zi=!0,m0(yo,function(){return zr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=vt.transition,vt.transition=null;var o=ne;ne=1;var a=ee;ee|=4,Rc.current=null,tw(e,n),i0(n,e),N1(Jl),xo=!!Zl,Jl=Zl=null,e.current=n,nw(n),Dx(),ee=a,ne=o,vt.transition=i}else e.current=n;if(zi&&(zi=!1,yn=e,Lo=s),i=e.pendingLanes,i===0&&(kn=null),Lx(n.stateNode),et(e,Se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Ro)throw Ro=!1,e=xu,xu=null,e;return Lo&1&&e.tag!==0&&zr(),i=e.pendingLanes,i&1?e===wu?Rs++:(Rs=0,wu=e):Rs=0,Mn(),null}function zr(){if(yn!==null){var e=Wp(Lo),t=vt.transition,n=ne;try{if(vt.transition=null,ne=16>e?16:e,yn===null)var r=!1;else{if(e=yn,yn=null,Lo=0,ee&6)throw Error(V(331));var s=ee;for(ee|=4,B=e.current;B!==null;){var i=B,o=i.child;if(B.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(B=c;B!==null;){var d=B;switch(d.tag){case 0:case 11:case 15:Ds(8,d,i)}var h=d.child;if(h!==null)h.return=d,B=h;else for(;B!==null;){d=B;var f=d.sibling,g=d.return;if(n0(d),d===c){B=null;break}if(f!==null){f.return=g,B=f;break}B=g}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}B=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,B=o;else e:for(;B!==null;){if(i=B,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ds(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,B=p;break e}B=i.return}}var m=e.current;for(B=m;B!==null;){o=B;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,B=y;else e:for(o=m;B!==null;){if(a=B,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ca(9,a)}}catch(C){ve(a,a.return,C)}if(a===o){B=null;break e}var b=a.sibling;if(b!==null){b.return=a.return,B=b;break e}B=a.return}}if(ee=s,Mn(),It&&typeof It.onPostCommitFiberRoot=="function")try{It.onPostCommitFiberRoot(na,e)}catch{}r=!0}return r}finally{ne=n,vt.transition=t}}return!1}function ih(e,t,n){t=Kr(n,t),t=Wg(e,t,1),e=bn(e,t,1),t=Ke(),e!==null&&(pi(e,1,t),et(e,t))}function ve(e,t,n){if(e.tag===3)ih(e,e,n);else for(;t!==null;){if(t.tag===3){ih(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(kn===null||!kn.has(r))){e=Kr(n,e),e=Kg(t,e,1),t=bn(t,e,1),e=Ke(),t!==null&&(pi(t,1,e),et(t,e));break}}t=t.return}}function lw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ke(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Me&n)===n&&(_e===4||_e===3&&(Me&130023424)===Me&&500>Se()-Vc?Jn(e,0):Lc|=n),et(e,t)}function f0(e,t){t===0&&(e.mode&1?(t=Pi,Pi<<=1,!(Pi&130023424)&&(Pi=4194304)):t=1);var n=Ke();e=nn(e,t),e!==null&&(pi(e,t,n),et(e,n))}function uw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),f0(e,n)}function cw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(V(314))}r!==null&&r.delete(t),f0(e,n)}var h0;h0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ze.current)Ye=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ye=!1,Y1(e,t,n);Ye=!!(e.flags&131072)}else Ye=!1,de&&t.flags&1048576&&yg(t,jo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;so(e,t),e=t.pendingProps;var s=Ur(t,$e.current);Or(t,n),s=Ec(null,t,r,e,s,n);var i=Pc();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(r)?(i=!0,To(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Tc(t),s.updater=ua,t.stateNode=s,s._reactInternals=t,lu(t,r,e,n),t=du(null,t,r,!0,i,n)):(t.tag=0,de&&i&&yc(t),Ue(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(so(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=fw(r),e=Tt(r,e),s){case 0:t=cu(null,t,r,e,n);break e;case 1:t=Qf(null,t,r,e,n);break e;case 11:t=Gf(null,t,r,e,n);break e;case 14:t=qf(null,t,r,Tt(r.type,e),n);break e}throw Error(V(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Tt(r,s),cu(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Tt(r,s),Qf(e,t,r,s,n);case 3:e:{if(Xg(t),e===null)throw Error(V(387));r=t.pendingProps,i=t.memoizedState,s=i.element,kg(e,t),Eo(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Kr(Error(V(423)),t),t=Xf(e,t,r,n,s);break e}else if(r!==s){s=Kr(Error(V(424)),t),t=Xf(e,t,r,n,s);break e}else for(rt=Sn(t.stateNode.containerInfo.firstChild),st=t,de=!0,jt=null,n=Sg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Br(),r===s){t=rn(e,t,n);break e}Ue(e,t,r,n)}t=t.child}return t;case 5:return Tg(t),e===null&&iu(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,eu(r,s)?o=null:i!==null&&eu(r,i)&&(t.flags|=32),Qg(e,t),Ue(e,t,o,n),t.child;case 6:return e===null&&iu(t),null;case 13:return Yg(e,t,n);case 4:return Cc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Hr(t,null,r,n):Ue(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Tt(r,s),Gf(e,t,r,s,n);case 7:return Ue(e,t,t.pendingProps,n),t.child;case 8:return Ue(e,t,t.pendingProps.children,n),t.child;case 12:return Ue(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,ie(No,r._currentValue),r._currentValue=o,i!==null)if(Dt(i.value,o)){if(i.children===s.children&&!Ze.current){t=rn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Xt(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),ou(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(V(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ou(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}Ue(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Or(t,n),s=xt(s),r=r(s),t.flags|=1,Ue(e,t,r,n),t.child;case 14:return r=t.type,s=Tt(r,t.pendingProps),s=Tt(r.type,s),qf(e,t,r,s,n);case 15:return Gg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Tt(r,s),so(e,t),t.tag=1,Je(r)?(e=!0,To(t)):e=!1,Or(t,n),Hg(t,r,s),lu(t,r,s,n),du(null,t,r,!0,e,n);case 19:return Zg(e,t,n);case 22:return qg(e,t,n)}throw Error(V(156,t.tag))};function m0(e,t){return $p(e,t)}function dw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,n,r){return new dw(e,t,n,r)}function zc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function fw(e){if(typeof e=="function")return zc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ic)return 11;if(e===oc)return 14}return 2}function Cn(e,t){var n=e.alternate;return n===null?(n=yt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ao(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")zc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case xr:return er(n.children,s,i,t);case sc:o=8,s|=8;break;case Dl:return e=yt(12,n,t,s|2),e.elementType=Dl,e.lanes=i,e;case Ml:return e=yt(13,n,t,s),e.elementType=Ml,e.lanes=i,e;case Rl:return e=yt(19,n,t,s),e.elementType=Rl,e.lanes=i,e;case Tp:return fa(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case bp:o=10;break e;case kp:o=9;break e;case ic:o=11;break e;case oc:o=14;break e;case fn:o=16,r=null;break e}throw Error(V(130,e==null?e:typeof e,""))}return t=yt(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function er(e,t,n,r){return e=yt(7,e,r,t),e.lanes=n,e}function fa(e,t,n,r){return e=yt(22,e,r,t),e.elementType=Tp,e.lanes=n,e.stateNode={isHidden:!1},e}function ol(e,t,n){return e=yt(6,e,null,t),e.lanes=n,e}function al(e,t,n){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function hw(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=$a(0),this.expirationTimes=$a(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$a(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function $c(e,t,n,r,s,i,o,a,u){return e=new hw(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=yt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Tc(i),e}function mw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:vr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function p0(e){if(!e)return Nn;e=e._reactInternals;e:{if(fr(e)!==e||e.tag!==1)throw Error(V(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(V(171))}if(e.tag===1){var n=e.type;if(Je(n))return pg(e,n,t)}return t}function g0(e,t,n,r,s,i,o,a,u){return e=$c(n,r,!0,e,s,i,o,a,u),e.context=p0(null),n=e.current,r=Ke(),s=Tn(n),i=Xt(r,s),i.callback=t??null,bn(n,i,s),e.current.lanes=s,pi(e,s,r),et(e,r),e}function ha(e,t,n,r){var s=t.current,i=Ke(),o=Tn(s);return n=p0(n),t.context===null?t.context=n:t.pendingContext=n,t=Xt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bn(s,t,o),e!==null&&(At(e,s,o,i),to(e,s,o)),o}function Io(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function oh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Uc(e,t){oh(e,t),(e=e.alternate)&&oh(e,t)}function pw(){return null}var y0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bc(e){this._internalRoot=e}ma.prototype.render=Bc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(V(409));ha(e,t,null,null)};ma.prototype.unmount=Bc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;lr(function(){ha(null,e,null,null)}),t[tn]=null}};function ma(e){this._internalRoot=e}ma.prototype.unstable_scheduleHydration=function(e){if(e){var t=qp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<mn.length&&t!==0&&t<mn[n].priority;n++);mn.splice(n,0,e),n===0&&Xp(e)}};function Hc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function pa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ah(){}function gw(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var c=Io(o);i.call(c)}}var o=g0(t,r,e,0,null,!1,!1,"",ah);return e._reactRootContainer=o,e[tn]=o.current,Qs(e.nodeType===8?e.parentNode:e),lr(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=Io(u);a.call(c)}}var u=$c(e,0,!1,null,null,!1,!1,"",ah);return e._reactRootContainer=u,e[tn]=u.current,Qs(e.nodeType===8?e.parentNode:e),lr(function(){ha(t,u,n,r)}),u}function ga(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var u=Io(o);a.call(u)}}ha(t,o,e,s)}else o=gw(n,t,e,s,r);return Io(o)}Kp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ws(t.pendingLanes);n!==0&&(uc(t,n|1),et(t,Se()),!(ee&6)&&(Gr=Se()+500,Mn()))}break;case 13:lr(function(){var r=nn(e,1);if(r!==null){var s=Ke();At(r,e,1,s)}}),Uc(e,1)}};cc=function(e){if(e.tag===13){var t=nn(e,134217728);if(t!==null){var n=Ke();At(t,e,134217728,n)}Uc(e,134217728)}};Gp=function(e){if(e.tag===13){var t=Tn(e),n=nn(e,t);if(n!==null){var r=Ke();At(n,e,t,r)}Uc(e,t)}};qp=function(){return ne};Qp=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};Hl=function(e,t,n){switch(t){case"input":if(Il(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=oa(r);if(!s)throw Error(V(90));jp(r),Il(r,s)}}}break;case"textarea":_p(e,n);break;case"select":t=n.value,t!=null&&Lr(e,!!n.multiple,t,!1)}};Lp=Ic;Vp=lr;var yw={usingClientEntryPoint:!1,Events:[yi,kr,oa,Mp,Rp,Ic]},hs={findFiberByHostInstance:qn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},vw={bundleType:hs.bundleType,version:hs.version,rendererPackageName:hs.rendererPackageName,rendererConfig:hs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:on.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Op(e),e===null?null:e.stateNode},findFiberByHostInstance:hs.findFiberByHostInstance||pw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $i=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$i.isDisabled&&$i.supportsFiber)try{na=$i.inject(vw),It=$i}catch{}}ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=yw;ct.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hc(t))throw Error(V(200));return mw(e,t,null,n)};ct.createRoot=function(e,t){if(!Hc(e))throw Error(V(299));var n=!1,r="",s=y0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=$c(e,1,!1,null,null,n,!1,r,s),e[tn]=t.current,Qs(e.nodeType===8?e.parentNode:e),new Bc(t)};ct.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(V(188)):(e=Object.keys(e).join(","),Error(V(268,e)));return e=Op(t),e=e===null?null:e.stateNode,e};ct.flushSync=function(e){return lr(e)};ct.hydrate=function(e,t,n){if(!pa(t))throw Error(V(200));return ga(null,e,t,!0,n)};ct.hydrateRoot=function(e,t,n){if(!Hc(e))throw Error(V(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=y0;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=g0(t,null,e,1,n??null,s,!1,i,o),e[tn]=t.current,Qs(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new ma(t)};ct.render=function(e,t,n){if(!pa(t))throw Error(V(200));return ga(null,e,t,!1,n)};ct.unmountComponentAtNode=function(e){if(!pa(e))throw Error(V(40));return e._reactRootContainer?(lr(function(){ga(null,null,e,!1,function(){e._reactRootContainer=null,e[tn]=null})}),!0):!1};ct.unstable_batchedUpdates=Ic;ct.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!pa(n))throw Error(V(200));if(e==null||e._reactInternals===void 0)throw Error(V(38));return ga(e,t,n,!1,r)};ct.version="18.3.1-next-f1338f8080-20240426";function v0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(v0)}catch(e){console.error(e)}}v0(),vp.exports=ct;var xw=vp.exports,lh=xw;Pl.createRoot=lh.createRoot,Pl.hydrateRoot=lh.hydrateRoot;const Wc=w.createContext({});function Kc(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}const ya=w.createContext(null),Gc=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class ww extends w.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Sw({children:e,isPresent:t}){const n=w.useId(),r=w.useRef(null),s=w.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=w.useContext(Gc);return w.useInsertionEffect(()=>{const{width:o,height:a,top:u,left:c}=s.current;if(t||!r.current||!o||!a)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return i&&(d.nonce=i),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${a}px !important;
            top: ${u}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),l.jsx(ww,{isPresent:t,childRef:r,sizeRef:s,children:w.cloneElement(e,{ref:r})})}const bw=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:i,mode:o})=>{const a=Kc(kw),u=w.useId(),c=w.useCallback(h=>{a.set(h,!0);for(const f of a.values())if(!f)return;r&&r()},[a,r]),d=w.useMemo(()=>({id:u,initial:t,isPresent:n,custom:s,onExitComplete:c,register:h=>(a.set(h,!1),()=>a.delete(h))}),i?[Math.random(),c]:[n,c]);return w.useMemo(()=>{a.forEach((h,f)=>a.set(f,!1))},[n]),w.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=l.jsx(Sw,{isPresent:n,children:e})),l.jsx(ya.Provider,{value:d,children:e})};function kw(){return new Map}function x0(e=!0){const t=w.useContext(ya);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,i=w.useId();w.useEffect(()=>{e&&s(i)},[e]);const o=w.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,o]:[!0]}const Ui=e=>e.key||"";function uh(e){const t=[];return w.Children.forEach(e,n=>{w.isValidElement(n)&&t.push(n)}),t}const qc=typeof window<"u",w0=qc?w.useLayoutEffect:w.useEffect,ae=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:i="sync",propagate:o=!1})=>{const[a,u]=x0(o),c=w.useMemo(()=>uh(e),[e]),d=o&&!a?[]:c.map(Ui),h=w.useRef(!0),f=w.useRef(c),g=Kc(()=>new Map),[v,x]=w.useState(c),[S,p]=w.useState(c);w0(()=>{h.current=!1,f.current=c;for(let b=0;b<S.length;b++){const C=Ui(S[b]);d.includes(C)?g.delete(C):g.get(C)!==!0&&g.set(C,!1)}},[S,d.length,d.join("-")]);const m=[];if(c!==v){let b=[...c];for(let C=0;C<S.length;C++){const j=S[C],_=Ui(j);d.includes(_)||(b.splice(C,0,j),m.push(j))}i==="wait"&&m.length&&(b=m),p(uh(b)),x(c);return}const{forceRender:y}=w.useContext(Wc);return l.jsx(l.Fragment,{children:S.map(b=>{const C=Ui(b),j=o&&!a?!1:c===S||d.includes(C),_=()=>{if(g.has(C))g.set(C,!0);else return;let T=!0;g.forEach(E=>{E||(T=!1)}),T&&(y==null||y(),p(f.current),o&&(u==null||u()),r&&r())};return l.jsx(bw,{isPresent:j,initial:!h.current||n?void 0:!1,custom:j?void 0:t,presenceAffectsLayout:s,mode:i,onExitComplete:j?void 0:_,children:b},C)})})},it=e=>e;let S0=it;function Qc(e){let t;return()=>(t===void 0&&(t=e()),t)}const qr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Yt=e=>e*1e3,Zt=e=>e/1e3,Tw={useManualTiming:!1};function Cw(e){let t=new Set,n=new Set,r=!1,s=!1;const i=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(c){i.has(c)&&(u.schedule(c),e()),c(o)}const u={schedule:(c,d=!1,h=!1)=>{const g=h&&r?t:n;return d&&i.add(c),g.has(c)||g.add(c),c},cancel:c=>{n.delete(c),i.delete(c)},process:c=>{if(o=c,r){s=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,s&&(s=!1,u.process(c))}};return u}const Bi=["read","resolveKeyframes","update","preRender","render","postRender"],jw=40;function b0(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,o=Bi.reduce((p,m)=>(p[m]=Cw(i),p),{}),{read:a,resolveKeyframes:u,update:c,preRender:d,render:h,postRender:f}=o,g=()=>{const p=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(p-s.timestamp,jw),1),s.timestamp=p,s.isProcessing=!0,a.process(s),u.process(s),c.process(s),d.process(s),h.process(s),f.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:Bi.reduce((p,m)=>{const y=o[m];return p[m]=(b,C=!1,j=!1)=>(n||v(),y.schedule(b,C,j)),p},{}),cancel:p=>{for(let m=0;m<Bi.length;m++)o[Bi[m]].cancel(p)},state:s,steps:o}}const{schedule:ue,cancel:_n,state:De,steps:ll}=b0(typeof requestAnimationFrame<"u"?requestAnimationFrame:it,!0),k0=w.createContext({strict:!1}),ch={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Qr={};for(const e in ch)Qr[e]={isEnabled:t=>ch[e].some(n=>!!t[n])};function Nw(e){for(const t in e)Qr[t]={...Qr[t],...e[t]}}const _w=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||_w.has(e)}let T0=e=>!Fo(e);function Ew(e){e&&(T0=t=>t.startsWith("on")?!Fo(t):e(t))}try{Ew(require("@emotion/is-prop-valid").default)}catch{}function Pw(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(T0(s)||n===!0&&Fo(s)||!t&&!Fo(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function Aw(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const va=w.createContext({});function si(e){return typeof e=="string"||Array.isArray(e)}function xa(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Xc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Yc=["initial",...Xc];function wa(e){return xa(e.animate)||Yc.some(t=>si(e[t]))}function C0(e){return!!(wa(e)||e.variants)}function Dw(e,t){if(wa(e)){const{initial:n,animate:r}=e;return{initial:n===!1||si(n)?n:void 0,animate:si(r)?r:void 0}}return e.inherit!==!1?t:{}}function Mw(e){const{initial:t,animate:n}=Dw(e,w.useContext(va));return w.useMemo(()=>({initial:t,animate:n}),[dh(t),dh(n)])}function dh(e){return Array.isArray(e)?e.join(" "):e}const Rw=Symbol.for("motionComponentSymbol");function Pr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Lw(e,t,n){return w.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Pr(n)&&(n.current=r))},[t])}const Zc=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Vw="framerAppearId",j0="data-"+Zc(Vw),{schedule:Jc}=b0(queueMicrotask,!1),N0=w.createContext({});function Iw(e,t,n,r,s){var i,o;const{visualElement:a}=w.useContext(va),u=w.useContext(k0),c=w.useContext(ya),d=w.useContext(Gc).reducedMotion,h=w.useRef(null);r=r||u.renderer,!h.current&&r&&(h.current=r(e,{visualState:t,parent:a,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:d}));const f=h.current,g=w.useContext(N0);f&&!f.projection&&s&&(f.type==="html"||f.type==="svg")&&Fw(h.current,n,s,g);const v=w.useRef(!1);w.useInsertionEffect(()=>{f&&v.current&&f.update(n,c)});const x=n[j0],S=w.useRef(!!x&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return w0(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Jc.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),w.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,x)}),S.current=!1))}),f}function Fw(e,t,n,r){const{layoutId:s,layout:i,drag:o,dragConstraints:a,layoutScroll:u,layoutRoot:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:_0(e.parent)),e.projection.setOptions({layoutId:s,layout:i,alwaysMeasureLayout:!!o||a&&Pr(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:u,layoutRoot:c})}function _0(e){if(e)return e.options.allowProjection!==!1?e.projection:_0(e.parent)}function Ow({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){var i,o;e&&Nw(e);function a(c,d){let h;const f={...w.useContext(Gc),...c,layoutId:zw(c)},{isStatic:g}=f,v=Mw(c),x=r(c,g);if(!g&&qc){$w();const S=Uw(f);h=S.MeasureLayout,v.visualElement=Iw(s,x,f,t,S.ProjectionNode)}return l.jsxs(va.Provider,{value:v,children:[h&&v.visualElement?l.jsx(h,{visualElement:v.visualElement,...f}):null,n(s,c,Lw(x,v.visualElement,d),x,g,v.visualElement)]})}a.displayName=`motion.${typeof s=="string"?s:`create(${(o=(i=s.displayName)!==null&&i!==void 0?i:s.name)!==null&&o!==void 0?o:""})`}`;const u=w.forwardRef(a);return u[Rw]=s,u}function zw({layoutId:e}){const t=w.useContext(Wc).id;return t&&e!==void 0?t+"-"+e:e}function $w(e,t){w.useContext(k0).strict}function Uw(e){const{drag:t,layout:n}=Qr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Bw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ed(e){return typeof e!="string"||e.includes("-")?!1:!!(Bw.indexOf(e)>-1||/[A-Z]/u.test(e))}function fh(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function td(e,t,n,r){if(typeof t=="function"){const[s,i]=fh(r);t=t(n!==void 0?n:e.custom,s,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,i]=fh(r);t=t(n!==void 0?n:e.custom,s,i)}return t}const ku=e=>Array.isArray(e),Hw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Ww=e=>ku(e)?e[e.length-1]||0:e,ze=e=>!!(e&&e.getVelocity);function lo(e){const t=ze(e)?e.get():e;return Hw(t)?t.toValue():t}function Kw({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,s,i){const o={latestValues:Gw(r,s,i,e),renderState:t()};return n&&(o.onMount=a=>n({props:r,current:a,...o}),o.onUpdate=a=>n(a)),o}const E0=e=>(t,n)=>{const r=w.useContext(va),s=w.useContext(ya),i=()=>Kw(e,t,r,s);return n?i():Kc(i)};function Gw(e,t,n,r){const s={},i=r(e,{});for(const f in i)s[f]=lo(i[f]);let{initial:o,animate:a}=e;const u=wa(e),c=C0(e);t&&c&&!u&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let d=n?n.initial===!1:!1;d=d||o===!1;const h=d?a:o;if(h&&typeof h!="boolean"&&!xa(h)){const f=Array.isArray(h)?h:[h];for(let g=0;g<f.length;g++){const v=td(e,f[g]);if(v){const{transitionEnd:x,transition:S,...p}=v;for(const m in p){let y=p[m];if(Array.isArray(y)){const b=d?y.length-1:0;y=y[b]}y!==null&&(s[m]=y)}for(const m in x)s[m]=x[m]}}}return s}const rs=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],hr=new Set(rs),P0=e=>t=>typeof t=="string"&&t.startsWith(e),A0=P0("--"),qw=P0("var(--"),nd=e=>qw(e)?Qw.test(e.split("/*")[0].trim()):!1,Qw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,D0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,sn=(e,t,n)=>n>t?t:n<e?e:n,ss={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ii={...ss,transform:e=>sn(0,1,e)},Hi={...ss,default:1},xi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),dn=xi("deg"),Ot=xi("%"),Q=xi("px"),Xw=xi("vh"),Yw=xi("vw"),hh={...Ot,parse:e=>Ot.parse(e)/100,transform:e=>Ot.transform(e*100)},Zw={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q},Jw={rotate:dn,rotateX:dn,rotateY:dn,rotateZ:dn,scale:Hi,scaleX:Hi,scaleY:Hi,scaleZ:Hi,skew:dn,skewX:dn,skewY:dn,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:ii,originX:hh,originY:hh,originZ:Q},mh={...ss,transform:Math.round},rd={...Zw,...Jw,zIndex:mh,size:Q,fillOpacity:ii,strokeOpacity:ii,numOctaves:mh},eS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tS=rs.length;function nS(e,t,n){let r="",s=!0;for(let i=0;i<tS;i++){const o=rs[i],a=e[o];if(a===void 0)continue;let u=!0;if(typeof a=="number"?u=a===(o.startsWith("scale")?1:0):u=parseFloat(a)===0,!u||n){const c=D0(a,rd[o]);if(!u){s=!1;const d=eS[o]||o;r+=`${d}(${c}) `}n&&(t[o]=c)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function sd(e,t,n){const{style:r,vars:s,transformOrigin:i}=e;let o=!1,a=!1;for(const u in t){const c=t[u];if(hr.has(u)){o=!0;continue}else if(A0(u)){s[u]=c;continue}else{const d=D0(c,rd[u]);u.startsWith("origin")?(a=!0,i[u]=d):r[u]=d}}if(t.transform||(o||n?r.transform=nS(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:u="50%",originY:c="50%",originZ:d=0}=i;r.transformOrigin=`${u} ${c} ${d}`}}const rS={offset:"stroke-dashoffset",array:"stroke-dasharray"},sS={offset:"strokeDashoffset",array:"strokeDasharray"};function iS(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?rS:sS;e[i.offset]=Q.transform(-r);const o=Q.transform(t),a=Q.transform(n);e[i.array]=`${o} ${a}`}function ph(e,t,n){return typeof e=="string"?e:Q.transform(t+n*e)}function oS(e,t,n){const r=ph(t,e.x,e.width),s=ph(n,e.y,e.height);return`${r} ${s}`}function id(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:u=0,...c},d,h){if(sd(e,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:g,dimensions:v}=e;f.transform&&(v&&(g.transform=f.transform),delete f.transform),v&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=oS(v,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&iS(f,o,a,u,!1)}const od=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),M0=()=>({...od(),attrs:{}}),ad=e=>typeof e=="string"&&e.toLowerCase()==="svg";function R0(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const L0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function V0(e,t,n,r){R0(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(L0.has(s)?s:Zc(s),t.attrs[s])}const Oo={};function aS(e){Object.assign(Oo,e)}function I0(e,{layout:t,layoutId:n}){return hr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Oo[e]||e==="opacity")}function ld(e,t,n){var r;const{style:s}=e,i={};for(const o in s)(ze(s[o])||t.style&&ze(t.style[o])||I0(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[o]=s[o]);return i}function F0(e,t,n){const r=ld(e,t,n);for(const s in e)if(ze(e[s])||ze(t[s])){const i=rs.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[i]=e[s]}return r}function lS(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const gh=["x","y","width","height","cx","cy","r"],uS={useVisualState:E0({scrapeMotionValuesFromProps:F0,createRenderState:M0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:s})=>{if(!n)return;let i=!!e.drag;if(!i){for(const a in s)if(hr.has(a)){i=!0;break}}if(!i)return;let o=!t;if(t)for(let a=0;a<gh.length;a++){const u=gh[a];e[u]!==t[u]&&(o=!0)}o&&ue.read(()=>{lS(n,r),ue.render(()=>{id(r,s,ad(n.tagName),e.transformTemplate),V0(n,r)})})}})},cS={useVisualState:E0({scrapeMotionValuesFromProps:ld,createRenderState:od})};function O0(e,t,n){for(const r in t)!ze(t[r])&&!I0(r,n)&&(e[r]=t[r])}function dS({transformTemplate:e},t){return w.useMemo(()=>{const n=od();return sd(n,t,e),Object.assign({},n.vars,n.style)},[t])}function fS(e,t){const n=e.style||{},r={};return O0(r,n,e),Object.assign(r,dS(e,t)),r}function hS(e,t){const n={},r=fS(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function mS(e,t,n,r){const s=w.useMemo(()=>{const i=M0();return id(i,t,ad(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};O0(i,e.style,e),s.style={...i,...s.style}}return s}function pS(e=!1){return(n,r,s,{latestValues:i},o)=>{const u=(ed(n)?mS:hS)(r,i,o,n),c=Pw(r,typeof n=="string",e),d=n!==w.Fragment?{...c,...u,ref:s}:{},{children:h}=r,f=w.useMemo(()=>ze(h)?h.get():h,[h]);return w.createElement(n,{...d,children:f})}}function gS(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...ed(r)?uS:cS,preloadedFeatures:e,useRender:pS(s),createVisualElement:t,Component:r};return Ow(o)}}function z0(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Sa(e,t,n){const r=e.getProps();return td(r,t,n!==void 0?n:r.custom,e)}const yS=Qc(()=>window.ScrollTimeline!==void 0);class vS{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(s=>{if(yS()&&s.attachTimeline)return s.attachTimeline(t);if(typeof n=="function")return n(s)});return()=>{r.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class xS extends vS{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function ud(e,t){return e?e[t]||e.default||e:void 0}const Tu=2e4;function $0(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Tu;)t+=n,r=e.next(t);return t>=Tu?1/0:t}function cd(e){return typeof e=="function"}function yh(e,t){e.timeline=t,e.onfinish=null}const dd=e=>Array.isArray(e)&&typeof e[0]=="number",wS={linearEasing:void 0};function SS(e,t){const n=Qc(e);return()=>{var r;return(r=wS[t])!==null&&r!==void 0?r:n()}}const zo=SS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),U0=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let i=0;i<s;i++)r+=e(qr(0,s-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function B0(e){return!!(typeof e=="function"&&zo()||!e||typeof e=="string"&&(e in Cu||zo())||dd(e)||Array.isArray(e)&&e.every(B0))}const bs=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Cu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:bs([0,.65,.55,1]),circOut:bs([.55,0,1,.45]),backIn:bs([.31,.01,.66,-.59]),backOut:bs([.33,1.53,.69,.99])};function H0(e,t){if(e)return typeof e=="function"&&zo()?U0(e,t):dd(e)?bs(e):Array.isArray(e)?e.map(n=>H0(n,t)||Cu.easeOut):Cu[e]}const kt={x:!1,y:!1};function W0(){return kt.x||kt.y}function bS(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let s=document;const i=(r=void 0)!==null&&r!==void 0?r:s.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function K0(e,t){const n=bS(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function vh(e){return t=>{t.pointerType==="touch"||W0()||e(t)}}function kS(e,t,n={}){const[r,s,i]=K0(e,n),o=vh(a=>{const{target:u}=a,c=t(a);if(typeof c!="function"||!u)return;const d=vh(h=>{c(h),u.removeEventListener("pointerleave",d)});u.addEventListener("pointerleave",d,s)});return r.forEach(a=>{a.addEventListener("pointerenter",o,s)}),i}const G0=(e,t)=>t?e===t?!0:G0(e,t.parentElement):!1,fd=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,TS=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function CS(e){return TS.has(e.tagName)||e.tabIndex!==-1}const ks=new WeakSet;function xh(e){return t=>{t.key==="Enter"&&e(t)}}function ul(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const jS=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=xh(()=>{if(ks.has(n))return;ul(n,"down");const s=xh(()=>{ul(n,"up")}),i=()=>ul(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function wh(e){return fd(e)&&!W0()}function NS(e,t,n={}){const[r,s,i]=K0(e,n),o=a=>{const u=a.currentTarget;if(!wh(a)||ks.has(u))return;ks.add(u);const c=t(a),d=(g,v)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!wh(g)||!ks.has(u))&&(ks.delete(u),typeof c=="function"&&c(g,{success:v}))},h=g=>{d(g,n.useGlobalTarget||G0(u,g.target))},f=g=>{d(g,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",f,s)};return r.forEach(a=>{!CS(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,s),a.addEventListener("focus",c=>jS(c,s),s)}),i}function _S(e){return e==="x"||e==="y"?kt[e]?null:(kt[e]=!0,()=>{kt[e]=!1}):kt.x||kt.y?null:(kt.x=kt.y=!0,()=>{kt.x=kt.y=!1})}const q0=new Set(["width","height","top","left","right","bottom",...rs]);let uo;function ES(){uo=void 0}const zt={now:()=>(uo===void 0&&zt.set(De.isProcessing||Tw.useManualTiming?De.timestamp:performance.now()),uo),set:e=>{uo=e,queueMicrotask(ES)}};function hd(e,t){e.indexOf(t)===-1&&e.push(t)}function md(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class pd{constructor(){this.subscriptions=[]}add(t){return hd(this.subscriptions,t),()=>md(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const o=this.subscriptions[i];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Q0(e,t){return t?e*(1e3/t):0}const Sh=30,PS=e=>!isNaN(parseFloat(e));class AS{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,s=!0)=>{const i=zt.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=zt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=PS(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new pd);const r=this.events[t].add(n);return t==="change"?()=>{r(),ue.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=zt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Sh)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Sh);return Q0(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function oi(e,t){return new AS(e,t)}function DS(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,oi(n))}function MS(e,t){const n=Sa(e,t);let{transitionEnd:r={},transition:s={},...i}=n||{};i={...i,...r};for(const o in i){const a=Ww(i[o]);DS(e,o,a)}}function RS(e){return!!(ze(e)&&e.add)}function ju(e,t){const n=e.getValue("willChange");if(RS(n))return n.add(t)}function X0(e){return e.props[j0]}const Y0=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,LS=1e-7,VS=12;function IS(e,t,n,r,s){let i,o,a=0;do o=t+(n-t)/2,i=Y0(o,r,s)-e,i>0?n=o:t=o;while(Math.abs(i)>LS&&++a<VS);return o}function wi(e,t,n,r){if(e===t&&n===r)return it;const s=i=>IS(i,0,1,e,n);return i=>i===0||i===1?i:Y0(s(i),t,r)}const Z0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,J0=e=>t=>1-e(1-t),ey=wi(.33,1.53,.69,.99),gd=J0(ey),ty=Z0(gd),ny=e=>(e*=2)<1?.5*gd(e):.5*(2-Math.pow(2,-10*(e-1))),yd=e=>1-Math.sin(Math.acos(e)),ry=J0(yd),sy=Z0(yd),iy=e=>/^0[^.\s]+$/u.test(e);function FS(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||iy(e):!0}const Ls=e=>Math.round(e*1e5)/1e5,vd=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function OS(e){return e==null}const zS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,xd=(e,t)=>n=>!!(typeof n=="string"&&zS.test(n)&&n.startsWith(e)||t&&!OS(n)&&Object.prototype.hasOwnProperty.call(n,t)),oy=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,i,o,a]=r.match(vd);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},$S=e=>sn(0,255,e),cl={...ss,transform:e=>Math.round($S(e))},Yn={test:xd("rgb","red"),parse:oy("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+cl.transform(e)+", "+cl.transform(t)+", "+cl.transform(n)+", "+Ls(ii.transform(r))+")"};function US(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Nu={test:xd("#"),parse:US,transform:Yn.transform},Ar={test:xd("hsl","hue"),parse:oy("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Ot.transform(Ls(t))+", "+Ot.transform(Ls(n))+", "+Ls(ii.transform(r))+")"},Fe={test:e=>Yn.test(e)||Nu.test(e)||Ar.test(e),parse:e=>Yn.test(e)?Yn.parse(e):Ar.test(e)?Ar.parse(e):Nu.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Yn.transform(e):Ar.transform(e)},BS=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function HS(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(vd))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(BS))===null||n===void 0?void 0:n.length)||0)>0}const ay="number",ly="color",WS="var",KS="var(",bh="${}",GS=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ai(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let i=0;const a=t.replace(GS,u=>(Fe.test(u)?(r.color.push(i),s.push(ly),n.push(Fe.parse(u))):u.startsWith(KS)?(r.var.push(i),s.push(WS),n.push(u)):(r.number.push(i),s.push(ay),n.push(parseFloat(u))),++i,bh)).split(bh);return{values:n,split:a,indexes:r,types:s}}function uy(e){return ai(e).values}function cy(e){const{split:t,types:n}=ai(e),r=t.length;return s=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],s[o]!==void 0){const a=n[o];a===ay?i+=Ls(s[o]):a===ly?i+=Fe.transform(s[o]):i+=s[o]}return i}}const qS=e=>typeof e=="number"?0:e;function QS(e){const t=uy(e);return cy(e)(t.map(qS))}const En={test:HS,parse:uy,createTransformer:cy,getAnimatableNone:QS},XS=new Set(["brightness","contrast","saturate","opacity"]);function YS(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(vd)||[];if(!r)return e;const s=n.replace(r,"");let i=XS.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const ZS=/\b([a-z-]*)\(.*?\)/gu,_u={...En,getAnimatableNone:e=>{const t=e.match(ZS);return t?t.map(YS).join(" "):e}},JS={...rd,color:Fe,backgroundColor:Fe,outlineColor:Fe,fill:Fe,stroke:Fe,borderColor:Fe,borderTopColor:Fe,borderRightColor:Fe,borderBottomColor:Fe,borderLeftColor:Fe,filter:_u,WebkitFilter:_u},wd=e=>JS[e];function dy(e,t){let n=wd(e);return n!==_u&&(n=En),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const e2=new Set(["auto","none","0"]);function t2(e,t,n){let r=0,s;for(;r<e.length&&!s;){const i=e[r];typeof i=="string"&&!e2.has(i)&&ai(i).values.length&&(s=e[r]),r++}if(s&&n)for(const i of t)e[i]=dy(n,s)}const kh=e=>e===ss||e===Q,Th=(e,t)=>parseFloat(e.split(", ")[t]),Ch=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/u);if(s)return Th(s[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?Th(i[1],e):0}},n2=new Set(["x","y","z"]),r2=rs.filter(e=>!n2.has(e));function s2(e){const t=[];return r2.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Xr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Ch(4,13),y:Ch(5,14)};Xr.translateX=Xr.x;Xr.translateY=Xr.y;const tr=new Set;let Eu=!1,Pu=!1;function fy(){if(Pu){const e=Array.from(tr).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=s2(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([i,o])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Pu=!1,Eu=!1,tr.forEach(e=>e.complete()),tr.clear()}function hy(){tr.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Pu=!0)})}function i2(){hy(),fy()}class Sd{constructor(t,n,r,s,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tr.add(this),Eu||(Eu=!0,ue.read(hy),ue.resolveKeyframes(fy))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const o=s==null?void 0:s.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const u=r.readValue(n,a);u!=null&&(t[0]=u)}t[0]===void 0&&(t[0]=a),s&&o===void 0&&s.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tr.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tr.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const my=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),o2=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function a2(e){const t=o2.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function py(e,t,n=1){const[r,s]=a2(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const o=i.trim();return my(o)?parseFloat(o):o}return nd(s)?py(s,t,n+1):s}const gy=e=>t=>t.test(e),l2={test:e=>e==="auto",parse:e=>e},yy=[ss,Q,Ot,dn,Yw,Xw,l2],jh=e=>yy.find(gy(e));class vy extends Sd{constructor(t,n,r,s,i){super(t,n,r,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let u=0;u<t.length;u++){let c=t[u];if(typeof c=="string"&&(c=c.trim(),nd(c))){const d=py(c,n.current);d!==void 0&&(t[u]=d),u===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!q0.has(r)||t.length!==2)return;const[s,i]=t,o=jh(s),a=jh(i);if(o!==a)if(kh(o)&&kh(a))for(let u=0;u<t.length;u++){const c=t[u];typeof c=="string"&&(t[u]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)FS(t[s])&&r.push(s);r.length&&t2(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Xr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:s}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,a=s[o];s[o]=Xr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([u,c])=>{n.getValue(u).set(c)}),this.resolveNoneKeyframes()}}const Nh=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(En.test(e)||e==="0")&&!e.startsWith("url("));function u2(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function c2(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],o=Nh(s,t),a=Nh(i,t);return!o||!a?!1:u2(e)||(n==="spring"||cd(n))&&r}const d2=e=>e!==null;function ba(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(d2),i=t&&n!=="loop"&&t%2===1?0:s.length-1;return!i||r===void 0?s[i]:r}const f2=40;class xy{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=zt.now(),this.options={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>f2?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&i2(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=zt.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:i,delay:o,onComplete:a,onUpdate:u,isGenerator:c}=this.options;if(!c&&!c2(t,r,s,i))if(o)this.options.duration=0;else{u&&u(ba(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const me=(e,t,n)=>e+(t-e)*n;function dl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function h2({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,o=0;if(!t)s=i=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,u=2*n-a;s=dl(u,a,e+1/3),i=dl(u,a,e),o=dl(u,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}function $o(e,t){return n=>n>0?t:e}const fl=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},m2=[Nu,Yn,Ar],p2=e=>m2.find(t=>t.test(e));function _h(e){const t=p2(e);if(!t)return!1;let n=t.parse(e);return t===Ar&&(n=h2(n)),n}const Eh=(e,t)=>{const n=_h(e),r=_h(t);if(!n||!r)return $o(e,t);const s={...n};return i=>(s.red=fl(n.red,r.red,i),s.green=fl(n.green,r.green,i),s.blue=fl(n.blue,r.blue,i),s.alpha=me(n.alpha,r.alpha,i),Yn.transform(s))},g2=(e,t)=>n=>t(e(n)),Si=(...e)=>e.reduce(g2),Au=new Set(["none","hidden"]);function y2(e,t){return Au.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function v2(e,t){return n=>me(e,t,n)}function bd(e){return typeof e=="number"?v2:typeof e=="string"?nd(e)?$o:Fe.test(e)?Eh:S2:Array.isArray(e)?wy:typeof e=="object"?Fe.test(e)?Eh:x2:$o}function wy(e,t){const n=[...e],r=n.length,s=e.map((i,o)=>bd(i)(i,t[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}}function x2(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=bd(e[s])(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}}function w2(e,t){var n;const r=[],s={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const o=t.types[i],a=e.indexes[o][s[o]],u=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=u,s[o]++}return r}const S2=(e,t)=>{const n=En.createTransformer(t),r=ai(e),s=ai(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?Au.has(e)&&!s.values.length||Au.has(t)&&!r.values.length?y2(e,t):Si(wy(w2(r,s),s.values),n):$o(e,t)};function Sy(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?me(e,t,n):bd(e)(e,t)}const b2=5;function by(e,t,n){const r=Math.max(t-b2,0);return Q0(n-e(r),t-r)}const ye={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},hl=.001;function k2({duration:e=ye.duration,bounce:t=ye.bounce,velocity:n=ye.velocity,mass:r=ye.mass}){let s,i,o=1-t;o=sn(ye.minDamping,ye.maxDamping,o),e=sn(ye.minDuration,ye.maxDuration,Zt(e)),o<1?(s=c=>{const d=c*o,h=d*e,f=d-n,g=Du(c,o),v=Math.exp(-h);return hl-f/g*v},i=c=>{const h=c*o*e,f=h*n+n,g=Math.pow(o,2)*Math.pow(c,2)*e,v=Math.exp(-h),x=Du(Math.pow(c,2),o);return(-s(c)+hl>0?-1:1)*((f-g)*v)/x}):(s=c=>{const d=Math.exp(-c*e),h=(c-n)*e+1;return-hl+d*h},i=c=>{const d=Math.exp(-c*e),h=(n-c)*(e*e);return d*h});const a=5/e,u=C2(s,i,a);if(e=Yt(e),isNaN(u))return{stiffness:ye.stiffness,damping:ye.damping,duration:e};{const c=Math.pow(u,2)*r;return{stiffness:c,damping:o*2*Math.sqrt(r*c),duration:e}}}const T2=12;function C2(e,t,n){let r=n;for(let s=1;s<T2;s++)r=r-e(r)/t(r);return r}function Du(e,t){return e*Math.sqrt(1-t*t)}const j2=["duration","bounce"],N2=["stiffness","damping","mass"];function Ph(e,t){return t.some(n=>e[n]!==void 0)}function _2(e){let t={velocity:ye.velocity,stiffness:ye.stiffness,damping:ye.damping,mass:ye.mass,isResolvedFromDuration:!1,...e};if(!Ph(e,N2)&&Ph(e,j2))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,i=2*sn(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:ye.mass,stiffness:s,damping:i}}else{const n=k2(e);t={...t,...n,mass:ye.mass},t.isResolvedFromDuration=!0}return t}function ky(e=ye.visualDuration,t=ye.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const i=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:g}=_2({...n,velocity:-Zt(n.velocity||0)}),v=f||0,x=c/(2*Math.sqrt(u*d)),S=o-i,p=Zt(Math.sqrt(u/d)),m=Math.abs(S)<5;r||(r=m?ye.restSpeed.granular:ye.restSpeed.default),s||(s=m?ye.restDelta.granular:ye.restDelta.default);let y;if(x<1){const C=Du(p,x);y=j=>{const _=Math.exp(-x*p*j);return o-_*((v+x*p*S)/C*Math.sin(C*j)+S*Math.cos(C*j))}}else if(x===1)y=C=>o-Math.exp(-p*C)*(S+(v+p*S)*C);else{const C=p*Math.sqrt(x*x-1);y=j=>{const _=Math.exp(-x*p*j),T=Math.min(C*j,300);return o-_*((v+x*p*S)*Math.sinh(T)+C*S*Math.cosh(T))/C}}const b={calculatedDuration:g&&h||null,next:C=>{const j=y(C);if(g)a.done=C>=h;else{let _=0;x<1&&(_=C===0?Yt(v):by(y,C,j));const T=Math.abs(_)<=r,E=Math.abs(o-j)<=s;a.done=T&&E}return a.value=a.done?o:j,a},toString:()=>{const C=Math.min($0(b),Tu),j=U0(_=>b.next(C*_).value,C,30);return C+"ms "+j}};return b}function Ah({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:u,restDelta:c=.5,restSpeed:d}){const h=e[0],f={done:!1,value:h},g=T=>a!==void 0&&T<a||u!==void 0&&T>u,v=T=>a===void 0?u:u===void 0||Math.abs(a-T)<Math.abs(u-T)?a:u;let x=n*t;const S=h+x,p=o===void 0?S:o(S);p!==S&&(x=p-h);const m=T=>-x*Math.exp(-T/r),y=T=>p+m(T),b=T=>{const E=m(T),D=y(T);f.done=Math.abs(E)<=c,f.value=f.done?p:D};let C,j;const _=T=>{g(f.value)&&(C=T,j=ky({keyframes:[f.value,v(f.value)],velocity:by(y,T,f.value),damping:s,stiffness:i,restDelta:c,restSpeed:d}))};return _(0),{calculatedDuration:null,next:T=>{let E=!1;return!j&&C===void 0&&(E=!0,b(T),_(T)),C!==void 0&&T>=C?j.next(T-C):(!E&&b(T),f)}}}const E2=wi(.42,0,1,1),P2=wi(0,0,.58,1),Ty=wi(.42,0,.58,1),A2=e=>Array.isArray(e)&&typeof e[0]!="number",D2={linear:it,easeIn:E2,easeInOut:Ty,easeOut:P2,circIn:yd,circInOut:sy,circOut:ry,backIn:gd,backInOut:ty,backOut:ey,anticipate:ny},Dh=e=>{if(dd(e)){S0(e.length===4);const[t,n,r,s]=e;return wi(t,n,r,s)}else if(typeof e=="string")return D2[e];return e};function M2(e,t,n){const r=[],s=n||Sy,i=e.length-1;for(let o=0;o<i;o++){let a=s(e[o],e[o+1]);if(t){const u=Array.isArray(t)?t[o]||it:t;a=Si(u,a)}r.push(a)}return r}function R2(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(S0(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=M2(t,r,s),u=a.length,c=d=>{if(o&&d<e[0])return t[0];let h=0;if(u>1)for(;h<e.length-2&&!(d<e[h+1]);h++);const f=qr(e[h],e[h+1],d);return a[h](f)};return n?d=>c(sn(e[0],e[i-1],d)):c}function L2(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=qr(0,t,r);e.push(me(n,1,s))}}function V2(e){const t=[0];return L2(t,e.length-1),t}function I2(e,t){return e.map(n=>n*t)}function F2(e,t){return e.map(()=>t||Ty).splice(0,e.length-1)}function Uo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=A2(r)?r.map(Dh):Dh(r),i={done:!1,value:t[0]},o=I2(n&&n.length===t.length?n:V2(t),e),a=R2(o,t,{ease:Array.isArray(s)?s:F2(t,s)});return{calculatedDuration:e,next:u=>(i.value=a(u),i.done=u>=e,i)}}const O2=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ue.update(t,!0),stop:()=>_n(t),now:()=>De.isProcessing?De.timestamp:zt.now()}},z2={decay:Ah,inertia:Ah,tween:Uo,keyframes:Uo,spring:ky},$2=e=>e/100;class kd extends xy{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:u}=this.options;u&&u()};const{name:n,motionValue:r,element:s,keyframes:i}=this.options,o=(s==null?void 0:s.KeyframeResolver)||Sd,a=(u,c)=>this.onKeyframesResolved(u,c);this.resolver=new o(i,a,n,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:i,velocity:o=0}=this.options,a=cd(n)?n:z2[n]||Uo;let u,c;a!==Uo&&typeof t[0]!="number"&&(u=Si($2,Sy(t[0],t[1])),t=[0,100]);const d=a({...this.options,keyframes:t});i==="mirror"&&(c=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),d.calculatedDuration===null&&(d.calculatedDuration=$0(d));const{calculatedDuration:h}=d,f=h+s,g=f*(r+1)-s;return{generator:d,mirroredGenerator:c,mapPercentToKeyframes:u,calculatedDuration:h,resolvedDuration:f,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:T}=this.options;return{done:!0,value:T[T.length-1]}}const{finalKeyframe:s,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:u,calculatedDuration:c,totalDuration:d,resolvedDuration:h}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:g,repeatType:v,repeatDelay:x,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),m=this.speed>=0?p<0:p>d;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let y=this.currentTime,b=i;if(g){const T=Math.min(this.currentTime,d)/h;let E=Math.floor(T),D=T%1;!D&&T>=1&&(D=1),D===1&&E--,E=Math.min(E,g+1),!!(E%2)&&(v==="reverse"?(D=1-D,x&&(D-=x/h)):v==="mirror"&&(b=o)),y=sn(0,1,D)*h}const C=m?{done:!1,value:u[0]}:b.next(y);a&&(C.value=a(C.value));let{done:j}=C;!m&&c!==null&&(j=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const _=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&j);return _&&s!==void 0&&(C.value=ba(u,this.options,s)),S&&S(C.value),_&&this.finish(),C}get duration(){const{resolved:t}=this;return t?Zt(t.calculatedDuration):0}get time(){return Zt(this.currentTime)}set time(t){t=Yt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Zt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=O2,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=s):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const U2=new Set(["opacity","clipPath","filter","transform"]);function B2(e,t,n,{delay:r=0,duration:s=300,repeat:i=0,repeatType:o="loop",ease:a="easeInOut",times:u}={}){const c={[t]:n};u&&(c.offset=u);const d=H0(a,s);return Array.isArray(d)&&(c.easing=d),e.animate(c,{delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:i+1,direction:o==="reverse"?"alternate":"normal"})}const H2=Qc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Bo=10,W2=2e4;function K2(e){return cd(e.type)||e.type==="spring"||!B0(e.ease)}function G2(e,t){const n=new kd({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const s=[];let i=0;for(;!r.done&&i<W2;)r=n.sample(i),s.push(r.value),i+=Bo;return{times:void 0,keyframes:s,duration:i-Bo,ease:"linear"}}const Cy={anticipate:ny,backInOut:ty,circInOut:sy};function q2(e){return e in Cy}class Mh extends xy{constructor(t){super(t);const{name:n,motionValue:r,element:s,keyframes:i}=this.options;this.resolver=new vy(i,(o,a)=>this.onKeyframesResolved(o,a),n,r,s),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:s,ease:i,type:o,motionValue:a,name:u,startTime:c}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof i=="string"&&zo()&&q2(i)&&(i=Cy[i]),K2(this.options)){const{onComplete:h,onUpdate:f,motionValue:g,element:v,...x}=this.options,S=G2(t,x);t=S.keyframes,t.length===1&&(t[1]=t[0]),r=S.duration,s=S.times,i=S.ease,o="keyframes"}const d=B2(a.owner.current,u,t,{...this.options,duration:r,times:s,ease:i});return d.startTime=c??this.calcStartTime(),this.pendingTimeline?(yh(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:h}=this.options;a.set(ba(t,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:s,type:o,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Zt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Zt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Yt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return it;const{animation:r}=n;yh(r,t)}return it}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:s,type:i,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:d,onComplete:h,element:f,...g}=this.options,v=new kd({...g,keyframes:r,duration:s,type:i,ease:o,times:a,isGenerator:!0}),x=Yt(this.time);c.setWithVelocity(v.sample(x-Bo).value,v.sample(x).value,Bo)}const{onStop:u}=this.options;u&&u(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:s,repeatType:i,damping:o,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:u,transformTemplate:c}=n.owner.getProps();return H2()&&r&&U2.has(r)&&!u&&!c&&!s&&i!=="mirror"&&o!==0&&a!=="inertia"}}const Q2={type:"spring",stiffness:500,damping:25,restSpeed:10},X2=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Y2={type:"keyframes",duration:.8},Z2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},J2=(e,{keyframes:t})=>t.length>2?Y2:hr.has(e)?e.startsWith("scale")?X2(t[1]):Q2:Z2;function eb({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:o,repeatDelay:a,from:u,elapsed:c,...d}){return!!Object.keys(d).length}const Td=(e,t,n,r={},s,i)=>o=>{const a=ud(r,e)||{},u=a.delay||r.delay||0;let{elapsed:c=0}=r;c=c-Yt(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-c,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:i?void 0:s};eb(a)||(d={...d,...J2(e,d)}),d.duration&&(d.duration=Yt(d.duration)),d.repeatDelay&&(d.repeatDelay=Yt(d.repeatDelay)),d.from!==void 0&&(d.keyframes[0]=d.from);let h=!1;if((d.type===!1||d.duration===0&&!d.repeatDelay)&&(d.duration=0,d.delay===0&&(h=!0)),h&&!i&&t.get()!==void 0){const f=ba(d.keyframes,a);if(f!==void 0)return ue.update(()=>{d.onUpdate(f),d.onComplete()}),new xS([])}return!i&&Mh.supports(d)?new Mh(d):new kd(d)};function tb({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function jy(e,t,{delay:n=0,transitionOverride:r,type:s}={}){var i;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...u}=t;r&&(o=r);const c=[],d=s&&e.animationState&&e.animationState.getState()[s];for(const h in u){const f=e.getValue(h,(i=e.latestValues[h])!==null&&i!==void 0?i:null),g=u[h];if(g===void 0||d&&tb(d,h))continue;const v={delay:n,...ud(o||{},h)};let x=!1;if(window.MotionHandoffAnimation){const p=X0(e);if(p){const m=window.MotionHandoffAnimation(p,h,ue);m!==null&&(v.startTime=m,x=!0)}}ju(e,h),f.start(Td(h,f,g,e.shouldReduceMotion&&q0.has(h)?{type:!1}:v,e,x));const S=f.animation;S&&c.push(S)}return a&&Promise.all(c).then(()=>{ue.update(()=>{a&&MS(e,a)})}),c}function Mu(e,t,n={}){var r;const s=Sa(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(jy(e,s,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:d=0,staggerChildren:h,staggerDirection:f}=i;return nb(e,t,d+c,h,f,n)}:()=>Promise.resolve(),{when:u}=i;if(u){const[c,d]=u==="beforeChildren"?[o,a]:[a,o];return c().then(()=>d())}else return Promise.all([o(),a(n.delay)])}function nb(e,t,n=0,r=0,s=1,i){const o=[],a=(e.variantChildren.size-1)*r,u=s===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(e.variantChildren).sort(rb).forEach((c,d)=>{c.notify("AnimationStart",t),o.push(Mu(c,t,{...i,delay:n+u(d)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(o)}function rb(e,t){return e.sortNodePosition(t)}function sb(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>Mu(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=Mu(e,t,n);else{const s=typeof t=="function"?Sa(e,t,n.custom):t;r=Promise.all(jy(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const ib=Yc.length;function Ny(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Ny(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<ib;n++){const r=Yc[n],s=e.props[r];(si(s)||s===!1)&&(t[r]=s)}return t}const ob=[...Xc].reverse(),ab=Xc.length;function lb(e){return t=>Promise.all(t.map(({animation:n,options:r})=>sb(e,n,r)))}function ub(e){let t=lb(e),n=Rh(),r=!0;const s=u=>(c,d)=>{var h;const f=Sa(e,d,u==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:g,transitionEnd:v,...x}=f;c={...c,...x,...v}}return c};function i(u){t=u(e)}function o(u){const{props:c}=e,d=Ny(e.parent)||{},h=[],f=new Set;let g={},v=1/0;for(let S=0;S<ab;S++){const p=ob[S],m=n[p],y=c[p]!==void 0?c[p]:d[p],b=si(y),C=p===u?m.isActive:null;C===!1&&(v=S);let j=y===d[p]&&y!==c[p]&&b;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),m.protectedKeys={...g},!m.isActive&&C===null||!y&&!m.prevProp||xa(y)||typeof y=="boolean")continue;const _=cb(m.prevProp,y);let T=_||p===u&&m.isActive&&!j&&b||S>v&&b,E=!1;const D=Array.isArray(y)?y:[y];let I=D.reduce(s(p),{});C===!1&&(I={});const{prevResolvedValues:A={}}=m,R={...A,...I},X=U=>{T=!0,f.has(U)&&(E=!0,f.delete(U)),m.needsAnimating[U]=!0;const L=e.getValue(U);L&&(L.liveStyle=!1)};for(const U in R){const L=I[U],W=A[U];if(g.hasOwnProperty(U))continue;let Y=!1;ku(L)&&ku(W)?Y=!z0(L,W):Y=L!==W,Y?L!=null?X(U):f.add(U):L!==void 0&&f.has(U)?X(U):m.protectedKeys[U]=!0}m.prevProp=y,m.prevResolvedValues=I,m.isActive&&(g={...g,...I}),r&&e.blockInitialAnimation&&(T=!1),T&&(!(j&&_)||E)&&h.push(...D.map(U=>({animation:U,options:{type:p}})))}if(f.size){const S={};f.forEach(p=>{const m=e.getBaseTarget(p),y=e.getValue(p);y&&(y.liveStyle=!0),S[p]=m??null}),h.push({animation:S})}let x=!!h.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(h):Promise.resolve()}function a(u,c){var d;if(n[u].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var g;return(g=f.animationState)===null||g===void 0?void 0:g.setActive(u,c)}),n[u].isActive=c;const h=o(u);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:o,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=Rh(),r=!0}}}function cb(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!z0(t,e):!1}function Fn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Rh(){return{animate:Fn(!0),whileInView:Fn(),whileHover:Fn(),whileTap:Fn(),whileDrag:Fn(),whileFocus:Fn(),exit:Fn()}}class Rn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class db extends Rn{constructor(t){super(t),t.animationState||(t.animationState=ub(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();xa(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let fb=0;class hb extends Rn{constructor(){super(...arguments),this.id=fb++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const mb={animation:{Feature:db},exit:{Feature:hb}};function li(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function bi(e){return{point:{x:e.pageX,y:e.pageY}}}const pb=e=>t=>fd(t)&&e(t,bi(t));function Vs(e,t,n,r){return li(e,t,pb(n),r)}const Lh=(e,t)=>Math.abs(e-t);function gb(e,t){const n=Lh(e.x,t.x),r=Lh(e.y,t.y);return Math.sqrt(n**2+r**2)}class _y{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=pl(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=gb(h.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:v}=h,{timestamp:x}=De;this.history.push({...v,timestamp:x});const{onStart:S,onMove:p}=this.handlers;f||(S&&S(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ml(f,this.transformPagePoint),ue.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=pl(h.type==="pointercancel"?this.lastMoveEventInfo:ml(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(h,S),v&&v(h,S)},!fd(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const o=bi(t),a=ml(o,this.transformPagePoint),{point:u}=a,{timestamp:c}=De;this.history=[{...u,timestamp:c}];const{onSessionStart:d}=n;d&&d(t,pl(a,this.history)),this.removeListeners=Si(Vs(this.contextWindow,"pointermove",this.handlePointerMove),Vs(this.contextWindow,"pointerup",this.handlePointerUp),Vs(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),_n(this.updatePoint)}}function ml(e,t){return t?{point:t(e.point)}:e}function Vh(e,t){return{x:e.x-t.x,y:e.y-t.y}}function pl({point:e},t){return{point:e,delta:Vh(e,Ey(t)),offset:Vh(e,yb(t)),velocity:vb(t,.1)}}function yb(e){return e[0]}function Ey(e){return e[e.length-1]}function vb(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=Ey(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Yt(t)));)n--;if(!r)return{x:0,y:0};const i=Zt(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const o={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const Py=1e-4,xb=1-Py,wb=1+Py,Ay=.01,Sb=0-Ay,bb=0+Ay;function at(e){return e.max-e.min}function kb(e,t,n){return Math.abs(e-t)<=n}function Ih(e,t,n,r=.5){e.origin=r,e.originPoint=me(t.min,t.max,e.origin),e.scale=at(n)/at(t),e.translate=me(n.min,n.max,e.origin)-e.originPoint,(e.scale>=xb&&e.scale<=wb||isNaN(e.scale))&&(e.scale=1),(e.translate>=Sb&&e.translate<=bb||isNaN(e.translate))&&(e.translate=0)}function Is(e,t,n,r){Ih(e.x,t.x,n.x,r?r.originX:void 0),Ih(e.y,t.y,n.y,r?r.originY:void 0)}function Fh(e,t,n){e.min=n.min+t.min,e.max=e.min+at(t)}function Tb(e,t,n){Fh(e.x,t.x,n.x),Fh(e.y,t.y,n.y)}function Oh(e,t,n){e.min=t.min-n.min,e.max=e.min+at(t)}function Fs(e,t,n){Oh(e.x,t.x,n.x),Oh(e.y,t.y,n.y)}function Cb(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?me(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?me(n,e,r.max):Math.min(e,n)),e}function zh(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function jb(e,{top:t,left:n,bottom:r,right:s}){return{x:zh(e.x,n,s),y:zh(e.y,t,r)}}function $h(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Nb(e,t){return{x:$h(e.x,t.x),y:$h(e.y,t.y)}}function _b(e,t){let n=.5;const r=at(e),s=at(t);return s>r?n=qr(t.min,t.max-r,e.min):r>s&&(n=qr(e.min,e.max-s,t.min)),sn(0,1,n)}function Eb(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ru=.35;function Pb(e=Ru){return e===!1?e=0:e===!0&&(e=Ru),{x:Uh(e,"left","right"),y:Uh(e,"top","bottom")}}function Uh(e,t,n){return{min:Bh(e,t),max:Bh(e,n)}}function Bh(e,t){return typeof e=="number"?e:e[t]||0}const Hh=()=>({translate:0,scale:1,origin:0,originPoint:0}),Dr=()=>({x:Hh(),y:Hh()}),Wh=()=>({min:0,max:0}),xe=()=>({x:Wh(),y:Wh()});function mt(e){return[e("x"),e("y")]}function Dy({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Ab({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Db(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function gl(e){return e===void 0||e===1}function Lu({scale:e,scaleX:t,scaleY:n}){return!gl(e)||!gl(t)||!gl(n)}function Un(e){return Lu(e)||My(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function My(e){return Kh(e.x)||Kh(e.y)}function Kh(e){return e&&e!=="0%"}function Ho(e,t,n){const r=e-n,s=t*r;return n+s}function Gh(e,t,n,r,s){return s!==void 0&&(e=Ho(e,s,r)),Ho(e,n,r)+t}function Vu(e,t=0,n=1,r,s){e.min=Gh(e.min,t,n,r,s),e.max=Gh(e.max,t,n,r,s)}function Ry(e,{x:t,y:n}){Vu(e.x,t.translate,t.scale,t.originPoint),Vu(e.y,n.translate,n.scale,n.originPoint)}const qh=.999999999999,Qh=1.0000000000001;function Mb(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,o;for(let a=0;a<s;a++){i=n[a],o=i.projectionDelta;const{visualElement:u}=i.options;u&&u.props.style&&u.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Rr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ry(e,o)),r&&Un(i.latestValues)&&Rr(e,i.latestValues))}t.x<Qh&&t.x>qh&&(t.x=1),t.y<Qh&&t.y>qh&&(t.y=1)}function Mr(e,t){e.min=e.min+t,e.max=e.max+t}function Xh(e,t,n,r,s=.5){const i=me(e.min,e.max,s);Vu(e,t,n,i,r)}function Rr(e,t){Xh(e.x,t.x,t.scaleX,t.scale,t.originX),Xh(e.y,t.y,t.scaleY,t.scale,t.originY)}function Ly(e,t){return Dy(Db(e.getBoundingClientRect(),t))}function Rb(e,t,n){const r=Ly(e,n),{scroll:s}=t;return s&&(Mr(r.x,s.offset.x),Mr(r.y,s.offset.y)),r}const Vy=({current:e})=>e?e.ownerDocument.defaultView:null,Lb=new WeakMap;class Vb{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=xe(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=d=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(bi(d).point)},i=(d,h)=>{const{drag:f,dragPropagation:g,onDragStart:v}=this.getProps();if(f&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=_S(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),mt(S=>{let p=this.getAxisMotionValue(S).get()||0;if(Ot.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const y=m.layout.layoutBox[S];y&&(p=at(y)*(parseFloat(p)/100))}}this.originPoint[S]=p}),v&&ue.postRender(()=>v(d,h)),ju(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(d,h)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:S}=h;if(g&&this.currentDirection===null){this.currentDirection=Ib(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",h.point,S),this.updateAxis("y",h.point,S),this.visualElement.render(),x&&x(d,h)},a=(d,h)=>this.stop(d,h),u=()=>mt(d=>{var h;return this.getAnimationState(d)==="paused"&&((h=this.getAxisMotionValue(d).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new _y(t,{onSessionStart:s,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Vy(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&ue.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!Wi(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=Cb(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Pr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=jb(s.layoutBox,n):this.constraints=!1,this.elastic=Pb(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&mt(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Eb(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Pr(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=Rb(r,s.root,this.visualElement.getTransformPagePoint());let o=Nb(s.layout.layoutBox,i);if(n){const a=n(Ab(o));this.hasMutatedConstraints=!!a,a&&(o=Dy(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),u=this.constraints||{},c=mt(d=>{if(!Wi(d,n,this.currentDirection))return;let h=u&&u[d]||{};o&&(h={min:0,max:0});const f=s?200:1e6,g=s?40:1e7,v={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...i,...h};return this.startAxisValueAnimation(d,v)});return Promise.all(c).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return ju(this.visualElement,t),r.start(Td(t,r,0,n,this.visualElement,!1))}stopAnimation(){mt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){mt(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){mt(n=>{const{drag:r}=this.getProps();if(!Wi(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[n];i.set(t[n]-me(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Pr(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};mt(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const u=a.get();s[o]=_b({min:u,max:u},this.constraints[o])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),mt(o=>{if(!Wi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:u,max:c}=this.constraints[o];a.set(me(u,c,s[o]))})}addListeners(){if(!this.visualElement.current)return;Lb.set(this.visualElement,this);const t=this.visualElement.current,n=Vs(t,"pointerdown",u=>{const{drag:c,dragListener:d=!0}=this.getProps();c&&d&&this.start(u)}),r=()=>{const{dragConstraints:u}=this.getProps();Pr(u)&&u.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ue.read(r);const o=li(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(mt(d=>{const h=this.getAxisMotionValue(d);h&&(this.originPoint[d]+=u[d].translate,h.set(h.get()+u[d].translate))}),this.visualElement.render())});return()=>{o(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:o=Ru,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function Wi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Ib(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Fb extends Rn{constructor(t){super(t),this.removeGroupControls=it,this.removeListeners=it,this.controls=new Vb(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||it}unmount(){this.removeGroupControls(),this.removeListeners()}}const Yh=e=>(t,n)=>{e&&ue.postRender(()=>e(t,n))};class Ob extends Rn{constructor(){super(...arguments),this.removePointerDownListener=it}onPointerDown(t){this.session=new _y(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Vy(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Yh(t),onStart:Yh(n),onMove:r,onEnd:(i,o)=>{delete this.session,s&&ue.postRender(()=>s(i,o))}}}mount(){this.removePointerDownListener=Vs(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const co={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Zh(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ms={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(Q.test(e))e=parseFloat(e);else return e;const n=Zh(e,t.target.x),r=Zh(e,t.target.y);return`${n}% ${r}%`}},zb={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=En.parse(e);if(s.length>5)return r;const i=En.createTransformer(e),o=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,u=n.y.scale*t.y;s[0+o]/=a,s[1+o]/=u;const c=me(a,u,.5);return typeof s[2+o]=="number"&&(s[2+o]/=c),typeof s[3+o]=="number"&&(s[3+o]/=c),i(s)}};class $b extends w.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;aS(Ub),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),co.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,s||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||ue.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Jc.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Iy(e){const[t,n]=x0(),r=w.useContext(Wc);return l.jsx($b,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(N0),isPresent:t,safeToRemove:n})}const Ub={borderRadius:{...ms,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ms,borderTopRightRadius:ms,borderBottomLeftRadius:ms,borderBottomRightRadius:ms,boxShadow:zb};function Bb(e,t,n){const r=ze(e)?e:oi(e);return r.start(Td("",r,t,n)),r.animation}function Hb(e){return e instanceof SVGElement&&e.tagName!=="svg"}const Wb=(e,t)=>e.depth-t.depth;class Kb{constructor(){this.children=[],this.isDirty=!1}add(t){hd(this.children,t),this.isDirty=!0}remove(t){md(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Wb),this.isDirty=!1,this.children.forEach(t)}}function Gb(e,t){const n=zt.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(_n(r),e(i-t))};return ue.read(r,!0),()=>_n(r)}const Fy=["TopLeft","TopRight","BottomLeft","BottomRight"],qb=Fy.length,Jh=e=>typeof e=="string"?parseFloat(e):e,em=e=>typeof e=="number"||Q.test(e);function Qb(e,t,n,r,s,i){s?(e.opacity=me(0,n.opacity!==void 0?n.opacity:1,Xb(r)),e.opacityExit=me(t.opacity!==void 0?t.opacity:1,0,Yb(r))):i&&(e.opacity=me(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<qb;o++){const a=`border${Fy[o]}Radius`;let u=tm(t,a),c=tm(n,a);if(u===void 0&&c===void 0)continue;u||(u=0),c||(c=0),u===0||c===0||em(u)===em(c)?(e[a]=Math.max(me(Jh(u),Jh(c),r),0),(Ot.test(c)||Ot.test(u))&&(e[a]+="%")):e[a]=c}(t.rotate||n.rotate)&&(e.rotate=me(t.rotate||0,n.rotate||0,r))}function tm(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Xb=Oy(0,.5,ry),Yb=Oy(.5,.95,it);function Oy(e,t,n){return r=>r<e?0:r>t?1:n(qr(e,t,r))}function nm(e,t){e.min=t.min,e.max=t.max}function ht(e,t){nm(e.x,t.x),nm(e.y,t.y)}function rm(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function sm(e,t,n,r,s){return e-=t,e=Ho(e,1/n,r),s!==void 0&&(e=Ho(e,1/s,r)),e}function Zb(e,t=0,n=1,r=.5,s,i=e,o=e){if(Ot.test(t)&&(t=parseFloat(t),t=me(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=me(i.min,i.max,r);e===i&&(a-=t),e.min=sm(e.min,t,n,a,s),e.max=sm(e.max,t,n,a,s)}function im(e,t,[n,r,s],i,o){Zb(e,t[n],t[r],t[s],t.scale,i,o)}const Jb=["x","scaleX","originX"],ek=["y","scaleY","originY"];function om(e,t,n,r){im(e.x,t,Jb,n?n.x:void 0,r?r.x:void 0),im(e.y,t,ek,n?n.y:void 0,r?r.y:void 0)}function am(e){return e.translate===0&&e.scale===1}function zy(e){return am(e.x)&&am(e.y)}function lm(e,t){return e.min===t.min&&e.max===t.max}function tk(e,t){return lm(e.x,t.x)&&lm(e.y,t.y)}function um(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function $y(e,t){return um(e.x,t.x)&&um(e.y,t.y)}function cm(e){return at(e.x)/at(e.y)}function dm(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nk{constructor(){this.members=[]}add(t){hd(this.members,t),t.scheduleRender()}remove(t){if(md(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function rk(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((s||i||o)&&(r=`translate3d(${s}px, ${i}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:c,rotate:d,rotateX:h,rotateY:f,skewX:g,skewY:v}=n;c&&(r=`perspective(${c}px) ${r}`),d&&(r+=`rotate(${d}deg) `),h&&(r+=`rotateX(${h}deg) `),f&&(r+=`rotateY(${f}deg) `),g&&(r+=`skewX(${g}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,u=e.y.scale*t.y;return(a!==1||u!==1)&&(r+=`scale(${a}, ${u})`),r||"none"}const Bn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Ts=typeof window<"u"&&window.MotionDebug!==void 0,yl=["","X","Y","Z"],sk={visibility:"hidden"},fm=1e3;let ik=0;function vl(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Uy(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=X0(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ue,!(s||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Uy(r)}function By({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=ik++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Ts&&(Bn.totalNodes=Bn.resolvedTargetDeltas=Bn.recalculatedProjection=0),this.nodes.forEach(lk),this.nodes.forEach(hk),this.nodes.forEach(mk),this.nodes.forEach(uk),Ts&&window.MotionDebug.record(Bn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new Kb)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new pd),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const u=this.eventHandlers.get(o);u&&u.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Hb(o),this.instance=o;const{layoutId:u,layout:c,visualElement:d}=this.options;if(d&&!d.current&&d.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||u)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Gb(f,250),co.hasAnimatedSinceResize&&(co.hasAnimatedSinceResize=!1,this.nodes.forEach(mm))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&d&&(u||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||xk,{onLayoutAnimationStart:S,onLayoutAnimationComplete:p}=d.getProps(),m=!this.targetLayout||!$y(this.targetLayout,v)||g,y=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||f&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,y);const b={...ud(x,"layout"),onPlay:S,onComplete:p};(d.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else f||mm(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,_n(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(pk),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Uy(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const h=this.path[d];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:u}=this.options;if(a===void 0&&!u)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(hm);return}this.isUpdating||this.nodes.forEach(dk),this.isUpdating=!1,this.nodes.forEach(fk),this.nodes.forEach(ok),this.nodes.forEach(ak),this.clearAllSnapshots();const a=zt.now();De.delta=sn(0,1e3/60,a-De.timestamp),De.timestamp=a,De.isProcessing=!0,ll.update.process(De),ll.preRender.process(De),ll.render.process(De),De.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Jc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ck),this.sharedNodes.forEach(gk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ue.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ue.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=xe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const u=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:u,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:u}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!zy(this.projectionDelta),u=this.getTransformTemplate(),c=u?u(this.latestValues,""):void 0,d=c!==this.prevTransformTemplateValue;o&&(a||Un(this.latestValues)||d)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let u=this.removeElementScroll(a);return o&&(u=this.removeTransform(u)),wk(u),{animationId:this.root.animationId,measuredBox:a,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return xe();const u=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(Sk))){const{scroll:d}=this.root;d&&(Mr(u.x,d.offset.x),Mr(u.y,d.offset.y))}return u}removeElementScroll(o){var a;const u=xe();if(ht(u,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return u;for(let c=0;c<this.path.length;c++){const d=this.path[c],{scroll:h,options:f}=d;d!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&ht(u,o),Mr(u.x,h.offset.x),Mr(u.y,h.offset.y))}return u}applyTransform(o,a=!1){const u=xe();ht(u,o);for(let c=0;c<this.path.length;c++){const d=this.path[c];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Rr(u,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Un(d.latestValues)&&Rr(u,d.latestValues)}return Un(this.latestValues)&&Rr(u,this.latestValues),u}removeTransform(o){const a=xe();ht(a,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!Un(c.latestValues))continue;Lu(c.latestValues)&&c.updateSnapshot();const d=xe(),h=c.measurePageBox();ht(d,h),om(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return Un(this.latestValues)&&om(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==De.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const u=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=u.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=u.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=u.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==u;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=De.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Fs(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),ht(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=xe(),this.targetWithTransforms=xe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Tb(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ht(this.target,this.layout.layoutBox),Ry(this.target,this.targetDelta)):ht(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Fs(this.relativeTargetOrigin,this.target,g.target),ht(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Ts&&Bn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Lu(this.parent.latestValues)||My(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),u=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(c=!1),u&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===De.timestamp&&(c=!1),c)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;ht(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;Mb(this.layoutCorrected,this.treeScale,this.path,u),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=xe());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(rm(this.prevProjectionDelta.x,this.projectionDelta.x),rm(this.prevProjectionDelta.y,this.projectionDelta.y)),Is(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==g||!dm(this.projectionDelta.x,this.prevProjectionDelta.x)||!dm(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Ts&&Bn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const u=this.getStack();u&&u.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Dr(),this.projectionDelta=Dr(),this.projectionDeltaWithTransform=Dr()}setAnimationOrigin(o,a=!1){const u=this.snapshot,c=u?u.latestValues:{},d={...this.latestValues},h=Dr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=xe(),g=u?u.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,S=this.getStack(),p=!S||S.members.length<=1,m=!!(x&&!p&&this.options.crossfade===!0&&!this.path.some(vk));this.animationProgress=0;let y;this.mixTargetDelta=b=>{const C=b/1e3;pm(h.x,o.x,C),pm(h.y,o.y,C),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fs(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),yk(this.relativeTarget,this.relativeTargetOrigin,f,C),y&&tk(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=xe()),ht(y,this.relativeTarget)),x&&(this.animationValues=d,Qb(d,c,this.latestValues,C,m,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(_n(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ue.update(()=>{co.hasAnimatedSinceResize=!0,this.currentAnimation=Bb(0,fm,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fm),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:u,layout:c,latestValues:d}=o;if(!(!a||!u||!c)){if(this!==o&&this.layout&&c&&Hy(this.options.animationType,this.layout.layoutBox,c.layoutBox)){u=this.target||xe();const h=at(this.layout.layoutBox.x);u.x.min=o.target.x.min,u.x.max=u.x.min+h;const f=at(this.layout.layoutBox.y);u.y.min=o.target.y.min,u.y.max=u.y.min+f}ht(a,u),Rr(a,d),Is(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new nk),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:u}={}){const c=this.getStack();c&&c.promote(this,u),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:u}=o;if((u.z||u.rotate||u.rotateX||u.rotateY||u.rotateZ||u.skewX||u.skewY)&&(a=!0),!a)return;const c={};u.z&&vl("z",o,c,this.animationValues);for(let d=0;d<yl.length;d++)vl(`rotate${yl[d]}`,o,c,this.animationValues),vl(`skew${yl[d]}`,o,c,this.animationValues);o.render();for(const d in c)o.setStaticValue(d,c[d]),this.animationValues&&(this.animationValues[d]=c[d]);o.scheduleRender()}getProjectionStyles(o){var a,u;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sk;const c={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=lo(o==null?void 0:o.pointerEvents)||"",c.transform=d?d(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=lo(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Un(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=rk(this.projectionDeltaWithTransform,this.treeScale,f),d&&(c.transform=d(f,c.transform));const{x:g,y:v}=this.projectionDelta;c.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,h.animationValues?c.opacity=h===this?(u=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in Oo){if(f[x]===void 0)continue;const{correct:S,applyTo:p}=Oo[x],m=c.transform==="none"?f[x]:S(f[x],h);if(p){const y=p.length;for(let b=0;b<y;b++)c[p[b]]=m}else c[x]=m}return this.options.layoutId&&(c.pointerEvents=h===this?lo(o==null?void 0:o.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(hm),this.root.sharedNodes.clear()}}}function ok(e){e.updateLayout()}function ak(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;i==="size"?mt(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],g=at(f);f.min=r[h].min,f.max=f.min+g}):Hy(i,n.layoutBox,r)&&mt(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],g=at(r[h]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+g)});const a=Dr();Is(a,r,n.layoutBox);const u=Dr();o?Is(u,e.applyTransform(s,!0),n.measuredBox):Is(u,r,n.layoutBox);const c=!zy(a);let d=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:g}=h;if(f&&g){const v=xe();Fs(v,n.layoutBox,f.layoutBox);const x=xe();Fs(x,r,g.layoutBox),$y(v,x)||(d=!0),h.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:u,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function lk(e){Ts&&Bn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function uk(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ck(e){e.clearSnapshot()}function hm(e){e.clearMeasurements()}function dk(e){e.isLayoutDirty=!1}function fk(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function mm(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function hk(e){e.resolveTargetDelta()}function mk(e){e.calcProjection()}function pk(e){e.resetSkewAndRotation()}function gk(e){e.removeLeadSnapshot()}function pm(e,t,n){e.translate=me(t.translate,0,n),e.scale=me(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function gm(e,t,n,r){e.min=me(t.min,n.min,r),e.max=me(t.max,n.max,r)}function yk(e,t,n,r){gm(e.x,t.x,n.x,r),gm(e.y,t.y,n.y,r)}function vk(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const xk={duration:.45,ease:[.4,0,.1,1]},ym=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),vm=ym("applewebkit/")&&!ym("chrome/")?Math.round:it;function xm(e){e.min=vm(e.min),e.max=vm(e.max)}function wk(e){xm(e.x),xm(e.y)}function Hy(e,t,n){return e==="position"||e==="preserve-aspect"&&!kb(cm(t),cm(n),.2)}function Sk(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const bk=By({attachResizeListener:(e,t)=>li(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),xl={current:void 0},Wy=By({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!xl.current){const e=new bk({});e.mount(window),e.setOptions({layoutScroll:!0}),xl.current=e}return xl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),kk={pan:{Feature:Ob},drag:{Feature:Fb,ProjectionNode:Wy,MeasureLayout:Iy}};function wm(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,i=r[s];i&&ue.postRender(()=>i(t,bi(t)))}class Tk extends Rn{mount(){const{current:t}=this.node;t&&(this.unmount=kS(t,n=>(wm(this.node,n,"Start"),r=>wm(this.node,r,"End"))))}unmount(){}}class Ck extends Rn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Si(li(this.node.current,"focus",()=>this.onFocus()),li(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Sm(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),i=r[s];i&&ue.postRender(()=>i(t,bi(t)))}class jk extends Rn{mount(){const{current:t}=this.node;t&&(this.unmount=NS(t,n=>(Sm(this.node,n,"Start"),(r,{success:s})=>Sm(this.node,r,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Iu=new WeakMap,wl=new WeakMap,Nk=e=>{const t=Iu.get(e.target);t&&t(e)},_k=e=>{e.forEach(Nk)};function Ek({root:e,...t}){const n=e||document;wl.has(n)||wl.set(n,{});const r=wl.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(_k,{root:e,...t})),r[s]}function Pk(e,t,n){const r=Ek(t);return Iu.set(e,n),r.observe(e),()=>{Iu.delete(e),r.unobserve(e)}}const Ak={some:0,all:1};class Dk extends Rn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:Ak[s]},a=u=>{const{isIntersecting:c}=u;if(this.isInView===c||(this.isInView=c,i&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:d,onViewportLeave:h}=this.node.getProps(),f=c?d:h;f&&f(u)};return Pk(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Mk(t,n))&&this.startObserver()}unmount(){}}function Mk({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Rk={inView:{Feature:Dk},tap:{Feature:jk},focus:{Feature:Ck},hover:{Feature:Tk}},Lk={layout:{ProjectionNode:Wy,MeasureLayout:Iy}},Fu={current:null},Ky={current:!1};function Vk(){if(Ky.current=!0,!!qc)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Fu.current=e.matches;e.addListener(t),t()}else Fu.current=!1}const Ik=[...yy,Fe,En],Fk=e=>Ik.find(gy(e)),bm=new WeakMap;function Ok(e,t,n){for(const r in t){const s=t[r],i=n[r];if(ze(s))e.addValue(r,s);else if(ze(i))e.addValue(r,oi(s,{owner:e}));else if(i!==s)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=e.getStaticValue(r);e.addValue(r,oi(o!==void 0?o:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const km=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class zk{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Sd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=zt.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,ue.render(this.render,!1,!0))};const{latestValues:u,renderState:c,onUpdate:d}=o;this.onUpdate=d,this.latestValues=u,this.baseTarget={...u},this.initialValues=n.initial?{...u}:{},this.renderState=c,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=wa(n),this.isVariantNode=C0(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:h,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in f){const v=f[g];u[g]!==void 0&&ze(v)&&v.set(u[g],!1)}}mount(t){this.current=t,bm.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Ky.current||Vk(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Fu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){bm.delete(this.current),this.projection&&this.projection.unmount(),_n(this.notifyUpdate),_n(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=hr.has(t),s=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&ue.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),i(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Qr){const n=Qr[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):xe()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<km.length;r++){const s=km[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i="on"+s,o=t[i];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=Ok(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=oi(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let s=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return s!=null&&(typeof s=="string"&&(my(s)||iy(s))?s=parseFloat(s):!Fk(s)&&En.test(n)&&(s=dy(t,n)),this.setBaseTarget(t,ze(s)?s.get():s)),ze(s)?s.get():s}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let s;if(typeof r=="string"||typeof r=="object"){const o=td(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(s=o[t])}if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!ze(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new pd),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Gy extends zk{constructor(){super(...arguments),this.KeyframeResolver=vy}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ze(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function $k(e){return window.getComputedStyle(e)}class Uk extends Gy{constructor(){super(...arguments),this.type="html",this.renderInstance=R0}readValueFromInstance(t,n){if(hr.has(n)){const r=wd(n);return r&&r.default||0}else{const r=$k(t),s=(A0(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Ly(t,n)}build(t,n,r){sd(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return ld(t,n,r)}}class Bk extends Gy{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=xe}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(hr.has(n)){const r=wd(n);return r&&r.default||0}return n=L0.has(n)?n:Zc(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return F0(t,n,r)}build(t,n,r){id(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,s){V0(t,n,r,s)}mount(t){this.isSVGTag=ad(t.tagName),super.mount(t)}}const Hk=(e,t)=>ed(e)?new Bk(t):new Uk(t,{allowProjection:e!==w.Fragment}),Wk=gS({...mb,...Rk,...kk,...Lk},Hk),P=Aw(Wk),Kn=class Kn{constructor(){Le(this,"session",null);Le(this,"user",null);Le(this,"profile",null)}static getInstance(){return Kn.instance||(Kn.instance=new Kn),Kn.instance}async login(t){const n=t.username==="GOD"&&t.password==="123456";if(t.username==="GOD"&&t.password!=="123456")throw new Error("Invalid credentials");const r=n?"test-user-god":"mock-user-1",s={sessionId:"mock-session-"+Date.now(),userId:r,username:t.username,expiresAt:new Date(Date.now()+24*60*60*1e3)},i={id:r,username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},o={user_id:r,full_name:n?"God User (Test Account)":t.username,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.session=s,this.user=i,this.profile=o,localStorage.setItem("authSession",JSON.stringify(s)),s}async register(t){const n={id:"mock-user-"+Date.now(),username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},r={user_id:n.id,full_name:t.username,email:t.email,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.user=n,this.profile=r,{user:n,profile:r}}async logout(){this.session=null,this.user=null,this.profile=null,localStorage.removeItem("authSession")}async validateSession(){try{const t=localStorage.getItem("authSession");if(t){const n=JSON.parse(t);return new Date(n.expiresAt)>new Date?(this.session=n,!0):(localStorage.removeItem("authSession"),!1)}return!1}catch(t){return console.error("Session validation failed:",t),!1}}getSession(){return this.session}getUser(){return this.user}getProfile(){return this.profile}requiresMultiFactorAuth(){return!1}async verifyMFA(t){return this.session}async refreshSession(){return this.session?(this.session.expiresAt=new Date(Date.now()+24*60*60*1e3),localStorage.setItem("authSession",JSON.stringify(this.session)),this.session):null}async changePassword(t,n){console.log("Password changed (mock)")}async initialize(){await this.validateSession()}};Le(Kn,"instance");let Ou=Kn;const fe=Ou.getInstance();/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kk=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qy=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Gk={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qk=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:o,...a},u)=>w.createElement("svg",{ref:u,...Gk,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:qy("lucide",s),...a},[...o.map(([c,d])=>w.createElement(c,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=(e,t)=>{const n=w.forwardRef(({className:r,...s},i)=>w.createElement(qk,{ref:i,iconNode:t,className:qy(`lucide-${Kk(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qy=K("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qk=K("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xk=K("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xy=K("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=K("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=K("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=K("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=K("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yk=K("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=K("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=K("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cd=K("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=K("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=K("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tm=K("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ev=K("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zk=K("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jk=K("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=K("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=K("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eT=K("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tv=K("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=K("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tT=K("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cm=K("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=K("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nd=K("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jm=K("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nv=K("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nT=K("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr=K("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _d=K("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rT=K("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sT=K("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rv=K("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iT=K("OctagonAlert",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M15.312 2a2 2 0 0 1 1.414.586l4.688 4.688A2 2 0 0 1 22 8.688v6.624a2 2 0 0 1-.586 1.414l-4.688 4.688a2 2 0 0 1-1.414.586H8.688a2 2 0 0 1-1.414-.586l-4.688-4.688A2 2 0 0 1 2 15.312V8.688a2 2 0 0 1 .586-1.414l4.688-4.688A2 2 0 0 1 8.688 2z",key:"1fd625"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oT=K("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aT=K("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ka=K("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sv=K("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lT=K("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ed=K("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zu=K("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uT=K("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cT=K("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iv=K("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dT=K("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fT=K("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nm=K("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pd=K("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hT=K("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=K("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mT=K("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ta=K("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=K("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const St=K("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=K("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),pT=({onClose:e,onVerify:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(!1),[o,a]=w.useState(null),[u,c]=w.useState("totp"),d=async f=>{f.preventDefault(),i(!0),a(null);try{await t(n),e()}catch(g){a(g instanceof Error?g.message:"Verification failed")}finally{i(!1)}},h=async()=>{i(!0),a(null);try{await new Promise(f=>setTimeout(f,1e3)),a("Code sent successfully")}catch{a("Failed to send code")}finally{i(!1)}};return l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:f=>f.target===f.currentTarget&&e(),children:l.jsxs(P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"Two-Factor Authentication"}),l.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:l.jsx(St,{className:"w-6 h-6"})})]}),o&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:o}),l.jsxs("div",{className:"mb-6",children:[l.jsx("p",{className:"fa-body text-gray-600 mb-4",children:"Enter the verification code from your authenticator app"}),l.jsxs("div",{className:"flex space-x-2 mb-6",children:[l.jsxs("button",{onClick:()=>c("totp"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${u==="totp"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[l.jsx(dT,{className:"w-4 h-4 mr-2"}),"Authenticator App"]}),l.jsxs("button",{onClick:()=>c("email"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${u==="email"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[l.jsx(_d,{className:"w-4 h-4 mr-2"}),"Email"]})]})]}),l.jsxs("form",{onSubmit:d,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Verification Code"}),l.jsx("input",{id:"code",type:"text",value:n,onChange:f=>r(f.target.value),className:"fa-input w-full text-center text-lg tracking-widest",placeholder:"000000",maxLength:6,required:!0})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:h,disabled:s,className:"flex-1 fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Resend Code"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:s||n.length!==6,className:"flex-1 fa-button-primary py-3 px-4 rounded-lg font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed",children:s?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Verifying..."]}):"Verify"})]})]}),l.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200",children:l.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[l.jsx(cT,{className:"w-4 h-4 mr-2"}),l.jsx("span",{children:"Two-factor authentication adds an extra layer of security to your account"})]})})]})})},ov=w.createContext(void 0),gT=({children:e})=>{const[t,n]=w.useState(null),[r,s]=w.useState(null),[i,o]=w.useState(null),[a,u]=w.useState(!1),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!0),[x,S]=w.useState(null);w.useEffect(()=>{(async()=>{try{v(!0),await fe.initialize();const E=fe.getSession(),D=fe.getUser(),I=fe.getProfile();n(E),s(D),o(I),u(!!E&&!!D),d(fe.requiresMultiFactorAuth())}catch(E){console.error("Auth initialization error:",E),S("Failed to initialize authentication")}finally{v(!1)}})()},[]);const p=async(T,E)=>{try{v(!0),S(null);const D={username:T,password:E},I=await fe.login(D),A=fe.getUser(),R=fe.getProfile();n(I),s(A),o(R),u(!0),d(!1)}catch(D){if(D instanceof Error&&D.message==="MFA_REQUIRED")d(!0),f(!0),S(null);else{const I=D instanceof Error?D.message:"Login failed";throw S(I),D}}finally{v(!1)}},m=async T=>{try{v(!0),S(null);const E=await fe.verifyMFA(T),D=fe.getUser(),I=fe.getProfile();n(E),s(D),o(I),u(!0),d(!1),f(!1)}catch(E){const D=E instanceof Error?E.message:"MFA verification failed";throw S(D),E}finally{v(!1)}},y=async(T,E,D,I)=>{try{v(!0),S(null);const A={username:T,password:E,email:D,fullName:I};await fe.register(A),await p(T,E)}catch(A){const R=A instanceof Error?A.message:"Registration failed";throw S(R),A}finally{v(!1)}},b=async()=>{try{v(!0),S(null),await fe.logout(),n(null),s(null),o(null),u(!1),d(!1),f(!1)}catch(T){const E=T instanceof Error?T.message:"Logout failed";S(E)}finally{v(!1)}},_={session:t,user:r,profile:i,isAuthenticated:a,requiresMFA:c,login:p,register:y,verifyMFA:m,logout:b,refreshSession:async()=>{try{v(!0),S(null);const T=await fe.refreshSession();T?(n(T),u(!0)):await b()}catch(T){const E=T instanceof Error?T.message:"Session refresh failed";S(E)}finally{v(!1)}},changePassword:async(T,E)=>{try{v(!0),S(null),await fe.changePassword(T,E)}catch(D){const I=D instanceof Error?D.message:"Password change failed";throw S(I),D}finally{v(!1)}},loading:g,error:x};return l.jsxs(ov.Provider,{value:_,children:[e,h&&l.jsx(pT,{onClose:()=>f(!1),onVerify:m})]})},is=()=>{const e=w.useContext(ov);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},yT=()=>{const{session:e,refreshSession:t,logout:n}=is(),r=w.useCallback(async()=>{if(e){const s=new Date().getTime();if(new Date(e.expiresAt).getTime()-s<5*60*1e3)try{await t()}catch(o){console.error("Failed to refresh session:",o),await n()}}},[e,t,n]);return w.useEffect(()=>{r();const s=setInterval(r,60*1e3);return()=>clearInterval(s)},[r]),{session:e,checkSessionExpiry:r}},vT=w.createContext(void 0),xT=({children:e})=>{const[t,n]=w.useState(!1),[r,s]=w.useState(null);yT(),w.useEffect(()=>{(async()=>{try{if(window.electronAPI){const a=await window.electronAPI.system.getInfo();s(a)}n(!0)}catch(a){console.error("Failed to initialize application:",a),n(!0)}})()},[]);const i={isInitialized:t,electronAPI:window.electronAPI||null,systemInfo:r};return l.jsx(vT.Provider,{value:i,children:e})},av=w.createContext(void 0),wT=()=>{const e=w.useContext(av);if(!e)throw new Error("useTheme must be used within ThemeProvider");return e},ST=({children:e})=>{const[t,n]=w.useState("light");w.useEffect(()=>{const o=localStorage.getItem("fa-theme");if(o)n(o);else{const a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(a)}},[]),w.useEffect(()=>{document.documentElement.setAttribute("data-theme",t),localStorage.setItem("fa-theme",t),t==="dark"?document.body.style.background=`linear-gradient(135deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(59, 130, 246, 0.1) 100%)`:document.body.style.background=`linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1) 0%, 
        rgba(126, 211, 33, 0.05) 50%, 
        rgba(135, 206, 235, 0.1) 100%)`},[t]);const i={theme:t,toggleTheme:()=>{n(o=>o==="light"?"dark":"light")},setTheme:o=>{n(o)}};return l.jsx(av.Provider,{value:i,children:e})},bT=({onClose:e})=>{const{user:t,profile:n,changePassword:r}=is(),[s,i]=w.useState(""),[o,a]=w.useState(""),[u,c]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(null),[v,x]=w.useState(!1),S=async p=>{if(p.preventDefault(),o!==u){g({type:"error",text:"New passwords do not match"});return}if(o.length<12){g({type:"error",text:"Password must be at least 12 characters long"});return}x(!0),g(null);try{await r(s,o),g({type:"success",text:"Password changed successfully"}),i(""),a(""),c(""),h(!1)}catch(m){g({type:"error",text:m instanceof Error?m.message:"Failed to change password"})}finally{x(!1)}};return l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:p=>p.target===p.currentTarget&&e(),children:l.jsxs(P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"User Profile"}),l.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:l.jsx(St,{className:"w-6 h-6"})})]}),f&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-4 p-3 rounded-lg text-sm ${f.type==="success"?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:f.text}),l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex flex-col items-center",children:[l.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mb-4",children:l.jsx(Pn,{className:"w-10 h-10 text-white"})}),l.jsx("h3",{className:"fa-heading-3 text-gray-800",children:t==null?void 0:t.username}),(n==null?void 0:n.full_name)&&l.jsx("p",{className:"fa-body text-gray-600",children:n.full_name})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(Pn,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Username"}),l.jsx("p",{className:"fa-body text-gray-800",children:t==null?void 0:t.username})]})]}),(n==null?void 0:n.email)&&l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(_d,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Email"}),l.jsx("p",{className:"fa-body text-gray-800",children:n.email})]})]}),l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(Ln,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Member since"}),l.jsx("p",{className:"fa-body text-gray-800",children:t!=null&&t.created_at?new Date(t.created_at).toLocaleDateString():"Unknown"})]})]})]}),l.jsxs("div",{className:"pt-4",children:[l.jsxs("button",{onClick:()=>h(!d),className:"w-full fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 flex items-center justify-center",children:[l.jsx(nr,{className:"w-5 h-5 mr-2"}),d?"Cancel":"Change Password"]}),d&&l.jsxs(P.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:S,className:"mt-4 space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),l.jsx("input",{id:"currentPassword",type:"password",value:s,onChange:p=>i(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),l.jsx("input",{id:"newPassword",type:"password",value:o,onChange:p=>a(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),l.jsx("input",{id:"confirmPassword",type:"password",value:u,onChange:p=>c(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:v,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:v?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Changing..."]}):l.jsxs(l.Fragment,{children:[l.jsx(Ed,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})]})})},kT=({systemInfo:e,onToggleTheme:t,theme:n})=>{const{user:r,logout:s}=is(),[i,o]=w.useState(!1);return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift",onClick:t,children:n==="light"?l.jsx(hT,{className:"w-5 h-5 text-fa-blue-600"}):l.jsx(sT,{className:"w-5 h-5 text-fa-aqua-400"})}),l.jsx("h1",{className:"fa-heading-3 font-bold text-fa-gray-800",children:"Modern Todo"})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[r&&l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700",onClick:()=>o(!0),children:[l.jsx(Pn,{className:"w-5 h-5"}),l.jsx("span",{className:"ml-2 text-sm font-medium hidden md:inline",children:r.username})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift text-fa-gray-700",onClick:s,children:l.jsx("span",{className:"text-sm font-medium",children:"Logout"})})]}),l.jsxs("div",{className:"text-xs text-fa-gray-500",children:[e==null?void 0:e.platform," • ",e==null?void 0:e.version]})]})]}),i&&l.jsx(bT,{onClose:()=>o(!1)})]})},TT=()=>{const e=[{icon:Nd,label:"Dashboard",active:!0},{icon:nv,label:"All Tasks"},{icon:Ln,label:"Today"},{icon:Jt,label:"Projects"},{icon:mr,label:"Tags"},{icon:jd,label:"Favorites"},{icon:Ad,label:"Quick Add"}],t=[{name:"Personal",count:12},{name:"Work",count:8},{name:"Shopping",count:3},{name:"Health",count:5}];return l.jsxs("div",{className:"h-full fa-glass-panel rounded-r-2xl flex flex-col",children:[l.jsx("div",{className:"p-4 border-b border-fa-white-frosted",children:l.jsx("h2",{className:"fa-heading-3 font-bold text-fa-gray-800 mb-2",children:"Navigation"})}),l.jsxs("div",{className:"flex-1 overflow-y-auto p-2",children:[l.jsx("nav",{className:"space-y-1",children:e.map((n,r)=>{const s=n.icon;return l.jsxs(P.button,{whileHover:{x:4},whileTap:{scale:.98},className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all ${n.active?"bg-fa-white-frosted text-fa-blue-600 shadow-md":"text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"}`,children:[l.jsx(s,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:n.label})]},r)})}),l.jsxs("div",{className:"mt-8",children:[l.jsxs("div",{className:"flex items-center justify-between px-4 mb-3",children:[l.jsx("h3",{className:"fa-body font-semibold text-fa-gray-700",children:"Categories"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded-lg text-fa-blue-500 hover:bg-fa-white-glass",children:l.jsx(ka,{className:"w-4 h-4"})})]}),l.jsx("div",{className:"space-y-1",children:t.map((n,r)=>l.jsxs(P.button,{whileHover:{x:4},whileTap:{scale:.98},className:"w-full flex items-center justify-between px-4 py-2 rounded-lg text-left text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[l.jsx("span",{children:n.name}),l.jsx("span",{className:"text-xs bg-fa-white-glass px-2 py-1 rounded-full",children:n.count})]},r))})]})]}),l.jsx("div",{className:"p-4 border-t border-fa-white-frosted",children:l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[l.jsx(uT,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"Settings"})]})})]})},CT="modulepreload",jT=function(e,t){return new URL(e,t).href},_m={},NT=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){const o=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),u=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));s=Promise.allSettled(n.map(c=>{if(c=jT(c,r),c in _m)return;_m[c]=!0;const d=c.endsWith(".css"),h=d?'[rel="stylesheet"]':"";if(!!r)for(let v=o.length-1;v>=0;v--){const x=o[v];if(x.href===c&&(!d||x.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${h}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":CT,d||(g.as="script"),g.crossOrigin="",g.href=c,u&&g.setAttribute("nonce",u),document.head.appendChild(g),d)return new Promise((v,x)=>{g.addEventListener("load",v),g.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return s.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},_T={},Em=e=>{let t;const n=new Set,r=(d,h)=>{const f=typeof d=="function"?d(t):d;if(!Object.is(f,t)){const g=t;t=h??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(v=>v(t,g))}},s=()=>t,u={setState:r,getState:s,getInitialState:()=>c,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(_T?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,s,u);return u},ET=e=>e?Em(e):Em;var lv={exports:{}},uv={},cv={exports:{}},dv={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yr=w;function PT(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var AT=typeof Object.is=="function"?Object.is:PT,DT=Yr.useState,MT=Yr.useEffect,RT=Yr.useLayoutEffect,LT=Yr.useDebugValue;function VT(e,t){var n=t(),r=DT({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return RT(function(){s.value=n,s.getSnapshot=t,Sl(s)&&i({inst:s})},[e,n,t]),MT(function(){return Sl(s)&&i({inst:s}),e(function(){Sl(s)&&i({inst:s})})},[e]),LT(n),n}function Sl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!AT(e,n)}catch{return!0}}function IT(e,t){return t()}var FT=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?IT:VT;dv.useSyncExternalStore=Yr.useSyncExternalStore!==void 0?Yr.useSyncExternalStore:FT;cv.exports=dv;var OT=cv.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ca=w,zT=OT;function $T(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var UT=typeof Object.is=="function"?Object.is:$T,BT=zT.useSyncExternalStore,HT=Ca.useRef,WT=Ca.useEffect,KT=Ca.useMemo,GT=Ca.useDebugValue;uv.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var i=HT(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=KT(function(){function u(g){if(!c){if(c=!0,d=g,g=r(g),s!==void 0&&o.hasValue){var v=o.value;if(s(v,g))return h=v}return h=g}if(v=h,UT(d,g))return v;var x=r(g);return s!==void 0&&s(v,x)?(d=g,v):(d=g,h=x)}var c=!1,d,h,f=n===void 0?null:n;return[function(){return u(t())},f===null?void 0:function(){return u(f())}]},[t,n,r,s]);var a=BT(e,i[0],i[1]);return WT(function(){o.hasValue=!0,o.value=a},[a]),GT(a),a};lv.exports=uv;var qT=lv.exports;const QT=op(qT),fv={},{useDebugValue:XT}=Z,{useSyncExternalStoreWithSelector:YT}=QT;let Pm=!1;const ZT=e=>e;function JT(e,t=ZT,n){(fv?"production":void 0)!=="production"&&n&&!Pm&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Pm=!0);const r=YT(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return XT(r),r}const eC=e=>{(fv?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?ET(e):e,n=(r,s)=>JT(t,r,s);return Object.assign(n,t),n},Dd=e=>eC,tC={},nC=e=>(t,n,r)=>{const s=r.subscribe;return r.subscribe=(o,a,u)=>{let c=o;if(a){const d=(u==null?void 0:u.equalityFn)||Object.is;let h=o(r.getState());c=f=>{const g=o(f);if(!d(h,g)){const v=h;a(h=g,v)}},u!=null&&u.fireImmediately&&a(h,h)}return s(c)},e(t,n,r)},Md=nC;function rC(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var i;const o=u=>u===null?null:JSON.parse(u,void 0),a=(i=n.getItem(s))!=null?i:null;return a instanceof Promise?a.then(o):o(a)},setItem:(s,i)=>n.setItem(s,JSON.stringify(i,void 0)),removeItem:s=>n.removeItem(s)}}const di=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return di(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return di(r)(n)}}}},sC=(e,t)=>(n,r,s)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,p)=>({...p,...S}),...t},o=!1;const a=new Set,u=new Set;let c;try{c=i.getStorage()}catch{}if(!c)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...S)},r,s);const d=di(i.serialize),h=()=>{const S=i.partialize({...r()});let p;const m=d({state:S,version:i.version}).then(y=>c.setItem(i.name,y)).catch(y=>{p=y});if(p)throw p;return m},f=s.setState;s.setState=(S,p)=>{f(S,p),h()};const g=e((...S)=>{n(...S),h()},r,s);let v;const x=()=>{var S;if(!c)return;o=!1,a.forEach(m=>m(r()));const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,r()))||void 0;return di(c.getItem.bind(c))(i.name).then(m=>{if(m)return i.deserialize(m)}).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return i.migrate(m.state,m.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return m.state}).then(m=>{var y;return v=i.merge(m,(y=r())!=null?y:g),n(v,!0),h()}).then(()=>{p==null||p(v,void 0),o=!0,u.forEach(m=>m(v))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:S=>{i={...i,...S},S.getStorage&&(c=S.getStorage())},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>x(),hasHydrated:()=>o,onHydrate:S=>(a.add(S),()=>{a.delete(S)}),onFinishHydration:S=>(u.add(S),()=>{u.delete(S)})},x(),v||g},iC=(e,t)=>(n,r,s)=>{let i={storage:rC(()=>localStorage),partialize:x=>x,version:0,merge:(x,S)=>({...S,...x}),...t},o=!1;const a=new Set,u=new Set;let c=i.storage;if(!c)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...x)},r,s);const d=()=>{const x=i.partialize({...r()});return c.setItem(i.name,{state:x,version:i.version})},h=s.setState;s.setState=(x,S)=>{h(x,S),d()};const f=e((...x)=>{n(...x),d()},r,s);s.getInitialState=()=>f;let g;const v=()=>{var x,S;if(!c)return;o=!1,a.forEach(m=>{var y;return m((y=r())!=null?y:f)});const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,(x=r())!=null?x:f))||void 0;return di(c.getItem.bind(c))(i.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return[!0,i.migrate(m.state,m.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var y;const[b,C]=m;if(g=i.merge(C,(y=r())!=null?y:f),n(g,!0),b)return d()}).then(()=>{p==null||p(g,void 0),g=r(),o=!0,u.forEach(m=>m(g))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:x=>{i={...i,...x},x.storage&&(c=x.storage)},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>o,onHydrate:x=>(a.add(x),()=>{a.delete(x)}),onFinishHydration:x=>(u.add(x),()=>{u.delete(x)})},i.skipHydration||v(),g||f},oC=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((tC?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),sC(e,t)):iC(e,t),Rd=oC;var hv=Symbol.for("immer-nothing"),Am=Symbol.for("immer-draftable"),lt=Symbol.for("immer-state");function Nt(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Zr=Object.getPrototypeOf;function Jr(e){return!!e&&!!e[lt]}function ur(e){var t;return e?mv(e)||Array.isArray(e)||!!e[Am]||!!((t=e.constructor)!=null&&t[Am])||Na(e)||_a(e):!1}var aC=Object.prototype.constructor.toString();function mv(e){if(!e||typeof e!="object")return!1;const t=Zr(e);if(t===null)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===aC}function Ko(e,t){ja(e)===0?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function ja(e){const t=e[lt];return t?t.type_:Array.isArray(e)?1:Na(e)?2:_a(e)?3:0}function $u(e,t){return ja(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function pv(e,t,n){const r=ja(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function lC(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function Na(e){return e instanceof Map}function _a(e){return e instanceof Set}function Wn(e){return e.copy_||e.base_}function Uu(e,t){if(Na(e))return new Map(e);if(_a(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=mv(e);if(t===!0||t==="class_only"&&!n){const r=Object.getOwnPropertyDescriptors(e);delete r[lt];let s=Reflect.ownKeys(r);for(let i=0;i<s.length;i++){const o=s[i],a=r[o];a.writable===!1&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(r[o]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[o]})}return Object.create(Zr(e),r)}else{const r=Zr(e);if(r!==null&&n)return{...e};const s=Object.create(r);return Object.assign(s,e)}}function Ld(e,t=!1){return Ea(e)||Jr(e)||!ur(e)||(ja(e)>1&&(e.set=e.add=e.clear=e.delete=uC),Object.freeze(e),t&&Object.entries(e).forEach(([n,r])=>Ld(r,!0))),e}function uC(){Nt(2)}function Ea(e){return Object.isFrozen(e)}var cC={};function cr(e){const t=cC[e];return t||Nt(0,e),t}var fi;function gv(){return fi}function dC(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Dm(e,t){t&&(cr("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Bu(e){Hu(e),e.drafts_.forEach(fC),e.drafts_=null}function Hu(e){e===fi&&(fi=e.parent_)}function Mm(e){return fi=dC(fi,e)}function fC(e){const t=e[lt];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Rm(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return e!==void 0&&e!==n?(n[lt].modified_&&(Bu(t),Nt(4)),ur(e)&&(e=Go(t,e),t.parent_||qo(t,e)),t.patches_&&cr("Patches").generateReplacementPatches_(n[lt].base_,e,t.patches_,t.inversePatches_)):e=Go(t,n,[]),Bu(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==hv?e:void 0}function Go(e,t,n){if(Ea(t))return t;const r=t[lt];if(!r)return Ko(t,(s,i)=>Lm(e,r,t,s,i,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return qo(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const s=r.copy_;let i=s,o=!1;r.type_===3&&(i=new Set(s),s.clear(),o=!0),Ko(i,(a,u)=>Lm(e,r,s,a,u,n,o)),qo(e,s,!1),n&&e.patches_&&cr("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Lm(e,t,n,r,s,i,o){if(Jr(s)){const a=i&&t&&t.type_!==3&&!$u(t.assigned_,r)?i.concat(r):void 0,u=Go(e,s,a);if(pv(n,r,u),Jr(u))e.canAutoFreeze_=!1;else return}else o&&n.add(s);if(ur(s)&&!Ea(s)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Go(e,s),(!t||!t.scope_.parent_)&&typeof r!="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r)&&qo(e,s)}}function qo(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Ld(t,n)}function hC(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:gv(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let s=r,i=Vd;n&&(s=[r],i=hi);const{revoke:o,proxy:a}=Proxy.revocable(s,i);return r.draft_=a,r.revoke_=o,a}var Vd={get(e,t){if(t===lt)return e;const n=Wn(e);if(!$u(n,t))return mC(e,n,t);const r=n[t];return e.finalized_||!ur(r)?r:r===bl(e.base_,t)?(kl(e),e.copy_[t]=Ku(r,e)):r},has(e,t){return t in Wn(e)},ownKeys(e){return Reflect.ownKeys(Wn(e))},set(e,t,n){const r=yv(Wn(e),t);if(r!=null&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const s=bl(Wn(e),t),i=s==null?void 0:s[lt];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(lC(n,s)&&(n!==void 0||$u(e.base_,t)))return!0;kl(e),Wu(e)}return e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty(e,t){return bl(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,kl(e),Wu(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const n=Wn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){Nt(11)},getPrototypeOf(e){return Zr(e.base_)},setPrototypeOf(){Nt(12)}},hi={};Ko(Vd,(e,t)=>{hi[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});hi.deleteProperty=function(e,t){return hi.set.call(this,e,t,void 0)};hi.set=function(e,t,n){return Vd.set.call(this,e[0],t,n,e[0])};function bl(e,t){const n=e[lt];return(n?Wn(n):e)[t]}function mC(e,t,n){var s;const r=yv(t,n);return r?"value"in r?r.value:(s=r.get)==null?void 0:s.call(e.draft_):void 0}function yv(e,t){if(!(t in e))return;let n=Zr(e);for(;n;){const r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Zr(n)}}function Wu(e){e.modified_||(e.modified_=!0,e.parent_&&Wu(e.parent_))}function kl(e){e.copy_||(e.copy_=Uu(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var pC=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,n,r)=>{if(typeof t=="function"&&typeof n!="function"){const i=n;n=t;const o=this;return function(u=i,...c){return o.produce(u,d=>n.call(this,d,...c))}}typeof n!="function"&&Nt(6),r!==void 0&&typeof r!="function"&&Nt(7);let s;if(ur(t)){const i=Mm(this),o=Ku(t,void 0);let a=!0;try{s=n(o),a=!1}finally{a?Bu(i):Hu(i)}return Dm(i,r),Rm(s,i)}else if(!t||typeof t!="object"){if(s=n(t),s===void 0&&(s=t),s===hv&&(s=void 0),this.autoFreeze_&&Ld(s,!0),r){const i=[],o=[];cr("Patches").generateReplacementPatches_(t,s,i,o),r(i,o)}return s}else Nt(1,t)},this.produceWithPatches=(t,n)=>{if(typeof t=="function")return(o,...a)=>this.produceWithPatches(o,u=>t(u,...a));let r,s;return[this.produce(t,n,(o,a)=>{r=o,s=a}),r,s]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){ur(e)||Nt(8),Jr(e)&&(e=gC(e));const t=Mm(this),n=Ku(e,void 0);return n[lt].isManual_=!0,Hu(t),n}finishDraft(e,t){const n=e&&e[lt];(!n||!n.isManual_)&&Nt(9);const{scope_:r}=n;return Dm(r,t),Rm(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const s=t[n];if(s.path.length===0&&s.op==="replace"){e=s.value;break}}n>-1&&(t=t.slice(n+1));const r=cr("Patches").applyPatches_;return Jr(e)?r(e,t):this.produce(e,s=>r(s,t))}};function Ku(e,t){const n=Na(e)?cr("MapSet").proxyMap_(e,t):_a(e)?cr("MapSet").proxySet_(e,t):hC(e,t);return(t?t.scope_:gv()).drafts_.push(n),n}function gC(e){return Jr(e)||Nt(10,e),vv(e)}function vv(e){if(!ur(e)||Ea(e))return e;const t=e[lt];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=Uu(e,t.scope_.immer_.useStrictShallowCopy_)}else n=Uu(e,!0);return Ko(n,(r,s)=>{pv(n,r,vv(s))}),t&&(t.finalized_=!1),n}var ut=new pC,yC=ut.produce;ut.produceWithPatches.bind(ut);ut.setAutoFreeze.bind(ut);ut.setUseStrictShallowCopy.bind(ut);ut.applyPatches.bind(ut);ut.createDraft.bind(ut);ut.finishDraft.bind(ut);const vC=e=>(t,n,r)=>(r.setState=(s,i,...o)=>{const a=typeof s=="function"?yC(s):s;return t(a,i,...o)},e(r.setState,n,r)),Id=vC;class xC{constructor(){Le(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.todos))throw new Error("Electron API not available");const r=await window.electronAPI.todos[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTodos(t,n){return this.makeRequest("getAll",t,n)}async createTodo(t){return this.makeRequest("create",t)}async updateTodo(t,n){return this.makeRequest("update",t,n)}async deleteTodo(t){return this.makeRequest("delete",t)}async updateTodoStatus(t,n){return this.makeRequest("updateStatus",t,n)}async toggleTodoCompletion(t,n){const r=n==="completed"?"pending":"completed";return this.updateTodoStatus(t,r)}isOverdue(t){return t.due_date?new Date(t.due_date)<new Date&&t.status!=="completed":!1}getPriorityWeight(t){return{very_low:1,low:2,medium:3,high:4,very_high:5}[t]||3}formatDueDate(t){const n=new Date(t),r=new Date,s=n.getTime()-r.getTime(),i=Math.ceil(s/(1e3*60*60*24));return i<0?`${Math.abs(i)} days overdue`:i===0?"Due today":i===1?"Due tomorrow":i<=7?`Due in ${i} days`:n.toLocaleDateString("en-US",{month:"short",day:"numeric",year:n.getFullYear()!==r.getFullYear()?"numeric":void 0})}}const He=new xC;class wC{constructor(){Le(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.categories))throw new Error("Electron API not available");const r=await window.electronAPI.categories[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllCategories(){return this.makeRequest("getAll")}async createCategory(t){return this.makeRequest("create",t)}async updateCategory(t,n){return this.makeRequest("update",t,n)}async deleteCategory(t){return this.makeRequest("delete",t)}async reorderCategories(t){return this.makeRequest("reorder",t)}}const Ki=new wC;class SC{constructor(){Le(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.tags))throw new Error("Electron API not available");const r=await window.electronAPI.tags[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTags(){return this.makeRequest("getAll")}async getTagSuggestions(t,n=10){return this.makeRequest("getSuggestions",t,n)}async getPopularTags(t=20){return this.makeRequest("getPopular",t)}async getRecentTags(t=10){return this.makeRequest("getRecent",t)}async getTagStats(t){return this.makeRequest("getStats",t)}async getSmartSuggestions(t="",n=10){try{if(t.trim())return await this.getTagSuggestions(t,n);{const[r,s]=await Promise.all([this.getPopularTags(Math.ceil(n*.7)),this.getRecentTags(Math.ceil(n*.3))]),i=[...r,...s];return Array.from(new Set(i)).slice(0,n)}}catch(r){return console.error("Error getting smart suggestions:",r),[]}}async getAutocompleteSuggestions(t,n=[],r=5){try{let s=[];return t.trim()?s=await this.getTagSuggestions(t,r*2):s=await this.getSmartSuggestions("",r*2),s.filter(o=>!n.includes(o)).slice(0,r)}catch(s){return console.error("Error getting autocomplete suggestions:",s),[]}}}const Cs=new SC,Vm={status:[],priority:[],categoryId:[],tags:[],dueDateRange:{},searchQuery:"",sortBy:"position",sortOrder:"asc"},bC=()=>`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,kC=(e,t)=>e.filter(n=>{if(t.status.length>0&&!t.status.includes(n.status)||t.priority.length>0&&!t.priority.includes(n.priority)||t.categoryId.length>0&&(!n.category_id||!t.categoryId.includes(n.category_id))||t.tags.length>0&&!t.tags.some(s=>n.tags.includes(s)))return!1;if(t.dueDateRange.start||t.dueDateRange.end){if(!n.due_date)return!1;const r=new Date(n.due_date);if(t.dueDateRange.start&&r<t.dueDateRange.start||t.dueDateRange.end&&r>t.dueDateRange.end)return!1}if(t.searchQuery){const r=t.searchQuery.toLowerCase();if(![n.title,n.description||"",...n.tags].join(" ").toLowerCase().includes(r))return!1}return!0}),TC=(e,t,n)=>[...e].sort((s,i)=>{let o,a;switch(t){case"position":o=s.position,a=i.position;break;case"created_at":o=new Date(s.created_at),a=new Date(i.created_at);break;case"updated_at":o=new Date(s.updated_at),a=new Date(i.updated_at);break;case"due_date":o=s.due_date?new Date(s.due_date):new Date("9999-12-31"),a=i.due_date?new Date(i.due_date):new Date("9999-12-31");break;case"priority":const u={very_low:1,low:2,medium:3,high:4,very_high:5};o=u[s.priority],a=u[i.priority];break;case"title":o=s.title.toLowerCase(),a=i.title.toLowerCase();break;default:return 0}return o<a?n==="asc"?-1:1:o>a?n==="asc"?1:-1:0}),Pa=Dd()(Md(Id(Rd((e,t)=>({todos:[],categories:[],filters:Vm,isLoading:!1,error:null,selectedTodos:[],draggedTodo:null,totalCount:0,completedCount:0,pendingCount:0,loadTodos:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const r=await He.getAll("current-session");e(s=>{var i,o,a;s.todos=r.todos||[],s.totalCount=((i=r.todos)==null?void 0:i.length)||0,s.completedCount=((o=r.todos)==null?void 0:o.filter(u=>u.status==="completed").length)||0,s.pendingCount=((a=r.todos)==null?void 0:a.filter(u=>u.status==="pending").length)||0,s.isLoading=!1})}catch(n){e(r=>{r.error=n instanceof Error?n.message:"Failed to load todos",r.isLoading=!1})}},createTodo:async n=>{const r=bC(),s={id:r,...n,created_at:new Date,updated_at:new Date,is_deleted:!1};e(i=>{i.todos.unshift(s),i.totalCount+=1,s.status==="pending"&&(i.pendingCount+=1)});try{const o=await He.create("current-session",n);return e(a=>{const u=a.todos.findIndex(c=>c.id===r);u!==-1&&(a.todos[u]=o.todo)}),o.todo.id}catch(i){throw e(o=>{o.todos=o.todos.filter(a=>a.id!==r),o.totalCount-=1,s.status==="pending"&&(o.pendingCount-=1),o.error=i instanceof Error?i.message:"Failed to create todo"}),i}},updateTodo:async(n,r)=>{const s=t().todos.find(i=>i.id===n);if(s){e(i=>{const o=i.todos.findIndex(a=>a.id===n);o!==-1&&(i.todos[o]={...i.todos[o],...r,updated_at:new Date})});try{const o=await He.update("current-session",n,r);e(a=>{const u=a.todos.findIndex(c=>c.id===n);u!==-1&&(a.todos[u]=o.todo)})}catch(i){throw e(o=>{const a=o.todos.findIndex(u=>u.id===n);a!==-1&&(o.todos[a]=s),o.error=i instanceof Error?i.message:"Failed to update todo"}),i}}},deleteTodo:async n=>{const r=t().todos.find(s=>s.id===n);if(r){e(s=>{s.todos=s.todos.filter(i=>i.id!==n),s.totalCount-=1,r.status==="completed"?s.completedCount-=1:r.status==="pending"&&(s.pendingCount-=1)});try{await He.delete("current-session",n)}catch(s){throw e(i=>{i.todos.push(r),i.totalCount+=1,r.status==="completed"?i.completedCount+=1:r.status==="pending"&&(i.pendingCount+=1),i.error=s instanceof Error?s.message:"Failed to delete todo"}),s}}},toggleTodoComplete:async n=>{const r=t().todos.find(o=>o.id===n);if(!r)return;const s=r.status==="completed"?"pending":"completed",i={status:s,completed_at:s==="completed"?new Date:void 0};await t().updateTodo(n,i)},reorderTodos:async(n,r,s)=>{console.log("Reorder todos:",{sourceIndex:n,destinationIndex:r,categoryId:s})},createCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await Ki.createCategory(n);return e(s=>{s.categories.push(r),s.isLoading=!1}),r.id}catch(r){const s=r instanceof Error?r.message:"Failed to create category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},updateCategory:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await Ki.updateCategory(n,r);e(i=>{const o=i.categories.findIndex(a=>a.id===n);o!==-1&&(i.categories[o]=s),i.isLoading=!1})}catch(s){const i=s instanceof Error?s.message:"Failed to update category";throw e(o=>{o.error=i,o.isLoading=!1}),s}},deleteCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{await Ki.deleteCategory(n),e(r=>{r.categories=r.categories.filter(s=>s.id!==n),r.todos.forEach(s=>{s.category_id===n&&(s.category_id=void 0)}),r.isLoading=!1})}catch(r){const s=r instanceof Error?r.message:"Failed to delete category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},loadCategories:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const n=await Ki.getAllCategories();e(r=>{r.categories=n,r.isLoading=!1})}catch(n){const r=n instanceof Error?n.message:"Failed to load categories";throw e(s=>{s.error=r,s.isLoading=!1}),n}},setFilters:n=>{e(r=>{r.filters={...r.filters,...n}})},clearFilters:()=>{e(n=>{n.filters=Vm})},setSearchQuery:n=>{e(r=>{r.filters.searchQuery=n})},setSortConfig:(n,r)=>{e(s=>{s.filters.sortBy=n,s.filters.sortOrder=r})},getAllTags:()=>{const{todos:n}=t(),r=new Set;return n.forEach(s=>{s.tags.forEach(i=>r.add(i))}),Array.from(r).sort()},getTagsWithStats:async()=>{try{return await Cs.getAllTags()}catch(n){return console.error("Error getting tags with stats:",n),[]}},getTagSuggestions:async(n,r)=>{try{return await Cs.getTagSuggestions(n,r)}catch(s){return console.error("Error getting tag suggestions:",s),[]}},getPopularTags:async n=>{try{return await Cs.getPopularTags(n)}catch(r){return console.error("Error getting popular tags:",r),[]}},getRecentTags:async n=>{try{return await Cs.getRecentTags(n)}catch(r){return console.error("Error getting recent tags:",r),[]}},bulkUpdateTags:async(n,r)=>{const{todos:s}=t(),i=s.filter(o=>o.tags.includes(n));e(o=>{o.isLoading=!0,o.error=null});try{await Promise.all(i.map(o=>{const a=o.tags.map(u=>u===n?r:u);return He.updateTodo(o.id,{tags:a})})),e(o=>{o.todos.forEach(a=>{a.tags.includes(n)&&(a.tags=a.tags.map(u=>u===n?r:u))}),o.isLoading=!1})}catch(o){const a=o instanceof Error?o.message:"Failed to update tags";throw e(u=>{u.error=a,u.isLoading=!1}),o}},deleteTag:async n=>{const{todos:r}=t(),s=r.filter(i=>i.tags.includes(n));e(i=>{i.isLoading=!0,i.error=null});try{await Promise.all(s.map(i=>{const o=i.tags.filter(a=>a!==n);return He.updateTodo(i.id,{tags:o})})),e(i=>{i.todos.forEach(o=>{o.tags=o.tags.filter(a=>a!==n)}),i.filters.tags=i.filters.tags.filter(o=>o!==n),i.isLoading=!1})}catch(i){const o=i instanceof Error?i.message:"Failed to delete tag";throw e(a=>{a.error=o,a.isLoading=!1}),i}},selectTodo:n=>{e(r=>{r.selectedTodos.includes(n)||r.selectedTodos.push(n)})},deselectTodo:n=>{e(r=>{r.selectedTodos=r.selectedTodos.filter(s=>s!==n)})},selectAllTodos:()=>{e(n=>{const r=t().getFilteredTodos();n.selectedTodos=r.map(s=>s.id)})},clearSelection:()=>{e(n=>{n.selectedTodos=[]})},toggleTodoSelection:n=>{const{selectedTodos:r}=t();r.includes(n)?t().deselectTodo(n):t().selectTodo(n)},setDraggedTodo:n=>{e(r=>{r.draggedTodo=n})},getFilteredTodos:()=>{const{todos:n,filters:r}=t(),s=kC(n,r);return TC(s,r.sortBy,r.sortOrder)},getTodosByCategory:n=>{const{todos:r}=t();return r.filter(s=>s.category_id===n)},getCompletedTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="completed")},getPendingTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="pending")},getOverdueTodos:()=>{const{todos:n}=t(),r=new Date;return n.filter(s=>s.due_date&&new Date(s.due_date)<r&&s.status!=="completed")},getTodoStats:()=>{const{todos:n}=t(),r=new Date,s=n.filter(i=>i.due_date&&new Date(i.due_date)<r&&i.status!=="completed").length;return{total:n.length,completed:n.filter(i=>i.status==="completed").length,pending:n.filter(i=>i.status==="pending").length,overdue:s}}}),{name:"todo-store",partialize:e=>({filters:e.filters,selectedTodos:e.selectedTodos})}))));Pa.subscribe(e=>e.todos,e=>{NT(async()=>{const{overdueNotificationService:t}=await Promise.resolve().then(()=>RC);return{overdueNotificationService:t}},void 0,import.meta.url).then(({overdueNotificationService:t})=>{t.triggerCheck()}).catch(t=>{console.error("Error triggering overdue check:",t)})});const Im=e=>e?new Date>=e:!0,CC=e=>{if(!e)return 0;const t=new Date,n=e.getTime()-t.getTime();return Math.max(0,n)};Dd()(Md(Id(Rd((e,t)=>({user:null,profile:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,sessionId:null,sessionExpiry:null,lastActivity:null,login:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.login(n,r);e(i=>{i.user=s.user,i.session=s.session,i.sessionId=s.session.session_id,i.sessionExpiry=new Date(s.session.expires_at),i.lastActivity=new Date,i.isAuthenticated=!0,i.isLoading=!1}),await t().loadProfile()}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Login failed",i.isLoading=!1,i.isAuthenticated=!1}),s}},logout:async()=>{e(n=>{n.isLoading=!0});try{const{sessionId:n}=t();n&&await fe.logout(n)}catch(n){console.error("Logout error:",n)}finally{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.isLoading=!1,n.error=null})}},register:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await fe.register(n);e(s=>{s.user=r.user,s.session=r.session,s.sessionId=r.session.session_id,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isAuthenticated=!0,s.isLoading=!1}),await t().loadProfile()}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Registration failed",s.isLoading=!1,s.isAuthenticated=!1}),r}},refreshSession:async()=>{const{sessionId:n}=t();if(!n)throw new Error("No active session to refresh");e(r=>{r.isLoading=!0});try{const r=await fe.refreshSession(n);e(s=>{s.session=r.session,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isLoading=!1})}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Session refresh failed",s.isLoading=!1}),await t().logout(),r}},updateProfile:async n=>{const{user:r}=t();if(!r)throw new Error("No authenticated user");e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.updateProfile(r.id,n);e(i=>{i.profile=s.profile,i.isLoading=!1})}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Profile update failed",i.isLoading=!1}),s}},loadProfile:async()=>{const{user:n}=t();if(n)try{const r=await fe.getProfile(n.id);e(s=>{s.profile=r})}catch(r){console.error("Failed to load profile:",r)}},validateSession:async()=>{const{sessionId:n,sessionExpiry:r}=t();if(!n||!r)return!1;if(Im(r))return await t().logout(),!1;try{return await fe.validateSession(n)?(e(i=>{i.lastActivity=new Date}),!0):(await t().logout(),!1)}catch(s){return console.error("Session validation error:",s),await t().logout(),!1}},extendSession:async()=>{const{sessionId:n}=t();if(n)try{const r=await fe.extendSession(n);e(s=>{s.sessionExpiry=new Date(r.expiresAt),s.lastActivity=new Date})}catch(r){console.error("Failed to extend session:",r)}},clearSession:()=>{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.error=null})},clearError:()=>{e(n=>{n.error=null})},setError:n=>{e(r=>{r.error=n})},isSessionValid:()=>{const{sessionExpiry:n}=t();return!Im(n)},getSessionTimeRemaining:()=>{const{sessionExpiry:n}=t();return CC(n)},getUserDisplayName:()=>{const{user:n,profile:r}=t();return r!=null&&r.full_name?r.full_name:n!=null&&n.username?n.username:"User"}}),{name:"auth-store",partialize:e=>({user:e.user,profile:e.profile,session:e.session,sessionId:e.sessionId,sessionExpiry:e.sessionExpiry,lastActivity:e.lastActivity,isAuthenticated:e.isAuthenticated})}))));const jC=()=>`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,NC=e=>{const t=["light","dark","frutiger-aero"],n=t.indexOf(e);return t[(n+1)%t.length]},_C=Dd()(Md(Id(Rd((e,t)=>({theme:"frutiger-aero",sidebarOpen:!0,sidebarWidth:280,activeModal:null,modalData:null,notifications:[],globalLoading:!1,loadingStates:{},currentView:"todos",previousView:null,todoViewMode:"list",showCompletedTodos:!0,autoSaveEnabled:!0,compactMode:!1,showNotifications:!0,soundEnabled:!0,setTheme:n=>{e(r=>{r.theme=n}),document.documentElement.setAttribute("data-theme",n),localStorage.setItem("fa-theme",n)},toggleTheme:()=>{const n=t().theme,r=NC(n);t().setTheme(r)},initializeTheme:()=>{try{const n=localStorage.getItem("fa-theme");if(n&&["light","dark","frutiger-aero"].includes(n))t().setTheme(n);else{const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";t().setTheme(r)}}catch(n){console.error("Error initializing theme:",n),t().setTheme("light")}},toggleSidebar:()=>{e(n=>{n.sidebarOpen=!n.sidebarOpen})},setSidebarOpen:n=>{e(r=>{r.sidebarOpen=n})},setSidebarWidth:n=>{e(r=>{r.sidebarWidth=Math.max(200,Math.min(400,n))})},openModal:(n,r=null)=>{e(s=>{s.activeModal=n,s.modalData=r})},closeModal:()=>{e(n=>{n.activeModal=null,n.modalData=null})},addNotification:n=>{const r=jC(),s={id:r,...n,createdAt:new Date};return e(i=>{i.notifications.push(s)}),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(r)},s.duration),r},removeNotification:n=>{e(r=>{r.notifications=r.notifications.filter(s=>s.id!==n)})},clearNotifications:()=>{e(n=>{n.notifications=[]})},setGlobalLoading:n=>{e(r=>{r.globalLoading=n})},setLoadingState:(n,r)=>{e(s=>{r?s.loadingStates[n]=!0:delete s.loadingStates[n]})},clearLoadingState:n=>{e(r=>{delete r.loadingStates[n]})},setCurrentView:n=>{e(r=>{r.previousView=r.currentView,r.currentView=n})},setTodoViewMode:n=>{e(r=>{r.todoViewMode=n})},toggleCompletedTodos:()=>{e(n=>{n.showCompletedTodos=!n.showCompletedTodos})},setAutoSave:n=>{e(r=>{r.autoSaveEnabled=n})},setCompactMode:n=>{e(r=>{r.compactMode=n})},setShowNotifications:n=>{e(r=>{r.showNotifications=n})},setSoundEnabled:n=>{e(r=>{r.soundEnabled=n})},getActiveNotifications:()=>{const{notifications:n,showNotifications:r}=t();return r?n:[]},isLoading:n=>{const{globalLoading:r,loadingStates:s}=t();return n?s[n]||!1:r||Object.keys(s).length>0}}),{name:"ui-store",partialize:e=>({theme:e.theme,sidebarOpen:e.sidebarOpen,sidebarWidth:e.sidebarWidth,currentView:e.currentView,todoViewMode:e.todoViewMode,showCompletedTodos:e.showCompletedTodos,autoSaveEnabled:e.autoSaveEnabled,compactMode:e.compactMode,showNotifications:e.showNotifications,soundEnabled:e.soundEnabled})}))));function Gu(e){if(!e.due_date||e.status==="completed"||e.status==="cancelled")return{isOverdue:!1,severity:"none",daysOverdue:0,hoursOverdue:0,formattedDuration:""};const t=new Date,n=new Date(e.due_date),r=t.getTime()-n.getTime();if(r<=0)return{isOverdue:!1,severity:"none",daysOverdue:0,hoursOverdue:0,formattedDuration:""};const s=Math.floor(r/(1e3*60*60)),i=Math.floor(r/(1e3*60*60*24));let o="slight";i>=7?o="severe":i>=1&&(o="moderate");const a=EC(s,i);return{isOverdue:!0,severity:o,daysOverdue:i,hoursOverdue:s,formattedDuration:a}}function EC(e,t){return t>=1?t===1?"1 day overdue":`${t} days overdue`:e>=1?e===1?"1 hour overdue":`${e} hours overdue`:"Just overdue"}function PC(e){switch(e){case"slight":return{borderClass:"border-l-4 border-orange-400",bgClass:"bg-orange-50",textClass:"text-orange-600",iconClass:"text-orange-500",pulseClass:""};case"moderate":return{borderClass:"border-l-4 border-red-500",bgClass:"bg-red-50",textClass:"text-red-700",iconClass:"text-red-600",pulseClass:""};case"severe":return{borderClass:"border-l-4 border-red-600",bgClass:"bg-red-100",textClass:"text-red-800",iconClass:"text-red-700",pulseClass:"animate-pulse"};default:return{borderClass:"",bgClass:"",textClass:"",iconClass:"",pulseClass:""}}}function AC(e,t){if(!e.isOverdue)return!1;const s=(t?new Date().getTime()-t.getTime():1/0)/(1e3*60*60);switch(e.severity){case"slight":return!t||s>=4;case"moderate":return!t||s>=12;case"severe":return!t||s>=6;default:return!1}}function DC(e){switch(e){case"slight":return"low";case"moderate":return"normal";case"severe":return"high";default:return"low"}}function MC(e,t){const n=`Task Overdue: ${e.title}`,r=`This task is ${t.formattedDuration}. ${t.severity==="severe"?"Please address it urgently!":"Please review when possible."}`;return{title:n,message:r}}const Gn=class Gn{constructor(){Le(this,"intervalId",null);Le(this,"notificationHistory",new Map);Le(this,"isRunning",!1);Le(this,"checkIntervalMs",15*60*1e3);Le(this,"getTodos",null);Le(this,"addNotification",null);this.loadNotificationHistory()}static getInstance(){return Gn.instance||(Gn.instance=new Gn),Gn.instance}initialize(t,n){this.getTodos=t,this.addNotification=n}start(){this.isRunning||!this.getTodos||!this.addNotification||(this.isRunning=!0,this.checkOverdueTodos(),this.intervalId=setInterval(()=>{this.checkOverdueTodos()},this.checkIntervalMs),console.log("OverdueNotificationService started"))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null),this.isRunning=!1,console.log("OverdueNotificationService stopped")}async checkOverdueTodos(){if(!(!this.getTodos||!this.addNotification))try{const t=await this.getTodos(),n=t.filter(r=>Gu(r).isOverdue);for(const r of n)await this.processOverdueTodo(r);this.cleanupNotificationHistory(t)}catch(t){console.error("Error checking overdue todos:",t)}}async processOverdueTodo(t){const n=Gu(t),r=this.notificationHistory.get(t.id),s=r==null?void 0:r.lastNotificationTime;AC(n,s)&&(await this.sendOverdueNotification(t,n),this.updateNotificationHistory(t.id))}async sendOverdueNotification(t,n){if(!this.addNotification)return;const{title:r,message:s}=MC(t,n);DC(n.severity);let i="warning",o=5e3;n.severity==="severe"?(i="error",o=1e4):n.severity==="moderate"&&(o=7e3);try{this.addNotification({type:i,title:r,message:s,duration:o,action:{label:"View Task",onClick:()=>{console.log("Navigate to todo:",t.id)}}}),console.log(`Sent ${n.severity} overdue notification for todo: ${t.title}`)}catch(a){console.error("Error sending overdue notification:",a)}}updateNotificationHistory(t){const n=this.notificationHistory.get(t),r={todoId:t,lastNotificationTime:new Date,notificationCount:((n==null?void 0:n.notificationCount)||0)+1};this.notificationHistory.set(t,r),this.saveNotificationHistory()}cleanupNotificationHistory(t){const n=new Set(t.map(s=>s.id)),r=new Set(t.filter(s=>s.status==="completed"||s.status==="cancelled").map(s=>s.id));for(const[s]of this.notificationHistory)(!n.has(s)||r.has(s))&&this.notificationHistory.delete(s);this.saveNotificationHistory()}async triggerCheck(){this.isRunning&&await this.checkOverdueTodos()}getNotificationHistory(){return Array.from(this.notificationHistory.values())}clearNotificationHistory(){this.notificationHistory.clear(),this.saveNotificationHistory()}saveNotificationHistory(){try{const t=Array.from(this.notificationHistory.entries());localStorage.setItem("overdueNotificationHistory",JSON.stringify(t))}catch(t){console.error("Error saving notification history:",t)}}loadNotificationHistory(){try{const t=localStorage.getItem("overdueNotificationHistory");if(t){const n=JSON.parse(t);this.notificationHistory=new Map(n.map(([r,s])=>[r,{...s,lastNotificationTime:new Date(s.lastNotificationTime)}]))}}catch(t){console.error("Error loading notification history:",t),this.notificationHistory=new Map}}setCheckInterval(t){this.checkIntervalMs=t*60*1e3,this.isRunning&&(this.stop(),this.start())}};Le(Gn,"instance");let Qo=Gn;const qu=Qo.getInstance(),RC=Object.freeze(Object.defineProperty({__proto__:null,OverdueNotificationService:Qo,overdueNotificationService:qu},Symbol.toStringTag,{value:"Module"})),LC=()=>{try{const e=useUIStore.getState();if(e&&e.initializeTheme)e.initializeTheme();else{const t=localStorage.getItem("fa-theme");t?document.documentElement.setAttribute("data-theme",t):document.documentElement.setAttribute("data-theme","light")}VC(),console.log("Stores initialized successfully")}catch(e){console.error("Error initializing stores:",e)}},VC=()=>{try{const e=useTodoStore.getState(),t=useUIStore.getState();qu.initialize(async()=>{const n=useTodoStore.getState().todos;return n.length===0?(await useTodoStore.getState().loadTodos(),useTodoStore.getState().todos):n},n=>{t.addNotification(n)}),qu.start(),console.log("Overdue notification service initialized")}catch(e){console.error("Error initializing overdue notification service:",e)}};function IC(e,t){const[n,r]=w.useState(e);return w.useEffect(()=>{const s=setTimeout(()=>{r(e)},t);return()=>{clearTimeout(s)}},[e,t]),n}function FC(e,t,n=[]){const r=w.useRef(),s=w.useRef(e);w.useEffect(()=>{s.current=e},[e,...n]);const i=w.useCallback((...o)=>{r.current&&clearTimeout(r.current),r.current=setTimeout(()=>{s.current(...o)},t)},[t]);return w.useEffect(()=>()=>{r.current&&clearTimeout(r.current)},[]),i}const OC=({value:e,onChange:t,onFilterToggle:n,placeholder:r="Search todos...",showFilterButton:s=!0,isFilterActive:i=!1,suggestions:o=[],className:a=""})=>{const[u,c]=w.useState(!1),[d,h]=w.useState(!1),[f,g]=w.useState(-1),v=w.useRef(null),x=w.useRef(null),S=FC(t,300,[t]),p=o.filter(T=>T.toLowerCase().includes(e.toLowerCase())&&T!==e).slice(0,5),m=T=>{const E=T.target.value;t(E),S(E),g(-1),h(E.length>0&&p.length>0)},y=()=>{var T;t(""),h(!1),(T=v.current)==null||T.focus()},b=T=>{var E;t(T),h(!1),g(-1),(E=v.current)==null||E.focus()},C=T=>{var E,D;if(!d||p.length===0){T.key==="Escape"&&((E=v.current)==null||E.blur());return}switch(T.key){case"ArrowDown":T.preventDefault(),g(I=>I<p.length-1?I+1:0);break;case"ArrowUp":T.preventDefault(),g(I=>I>0?I-1:p.length-1);break;case"Enter":T.preventDefault(),f>=0&&b(p[f]);break;case"Escape":h(!1),g(-1),(D=v.current)==null||D.blur();break}},j=()=>{c(!0),e.length>0&&p.length>0&&h(!0)},_=()=>{c(!1),setTimeout(()=>{h(!1),g(-1)},150)};return w.useEffect(()=>{const T=E=>{var D;(E.metaKey||E.ctrlKey)&&E.key==="k"&&(E.preventDefault(),(D=v.current)==null||D.focus())};return document.addEventListener("keydown",T),()=>document.removeEventListener("keydown",T)},[]),l.jsxs("div",{className:`relative ${a}`,children:[l.jsx("div",{className:`fa-glass-panel relative transition-all duration-300 ${u?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"flex-shrink-0 pl-4",children:l.jsx(zu,{className:`w-5 h-5 transition-colors duration-200 ${u?"text-fa-blue-500":"text-fa-gray-400"}`})}),l.jsx("input",{ref:v,type:"text",value:e,onChange:m,onFocus:j,onBlur:_,onKeyDown:C,placeholder:r,className:"flex-1 bg-transparent px-4 py-3 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"}),!u&&!e&&l.jsx("div",{className:"flex-shrink-0 pr-2",children:l.jsxs("div",{className:"flex items-center space-x-1 text-fa-gray-400 text-sm",children:[l.jsx(Zk,{className:"w-3 h-3"}),l.jsx("span",{children:"K"})]})}),e&&l.jsx(P.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y,className:"flex-shrink-0 p-1 mx-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:l.jsx(St,{className:"w-4 h-4"})}),s&&l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:n,className:`flex-shrink-0 p-2 mx-2 rounded-lg transition-all duration-200 ${i?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,children:l.jsx(eT,{className:"w-4 h-4"})})]})}),l.jsx(ae,{children:d&&p.length>0&&l.jsx(P.div,{ref:x,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 right-0 mt-2 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto",children:p.map((T,E)=>l.jsx(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>b(T),className:`w-full text-left px-4 py-3 transition-colors duration-150 ${E===f?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"} ${E===0?"rounded-t-xl":""} ${E===p.length-1?"rounded-b-xl":""}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(zu,{className:"w-4 h-4 mr-3 text-fa-gray-400"}),l.jsx("span",{children:T})]})},T))})})]})},Qu={very_high:{label:"Very High",color:"text-red-600",bgColor:"bg-red-100",borderColor:"border-red-300",textColor:"text-red-800",icon:Ta,weight:5,gradient:"from-red-500 to-red-600",glowColor:"shadow-red-500/50"},high:{label:"High",color:"text-orange-600",bgColor:"bg-orange-100",borderColor:"border-orange-300",textColor:"text-orange-800",icon:Xy,weight:4,gradient:"from-orange-500 to-orange-600",glowColor:"shadow-orange-500/30"},medium:{label:"Medium",color:"text-yellow-600",bgColor:"bg-yellow-100",borderColor:"border-yellow-300",textColor:"text-yellow-800",icon:tv,weight:3,gradient:"from-yellow-500 to-yellow-600"},low:{label:"Low",color:"text-blue-600",bgColor:"bg-blue-100",borderColor:"border-blue-300",textColor:"text-blue-800",icon:Qy,weight:2,gradient:"from-blue-500 to-blue-600"},very_low:{label:"Very Low",color:"text-gray-500",bgColor:"bg-gray-100",borderColor:"border-gray-300",textColor:"text-gray-700",icon:rT,weight:1,gradient:"from-gray-400 to-gray-500"}};function Vn(e){return Qu[e]||Qu.medium}function Fm(e){return Vn(e).color}function Tl(e){return Vn(e).bgColor}function Cl(e){return Vn(e).borderColor}function cn(e){return Vn(e).textColor}function zC(e){return Vn(e).icon}function On(e){return Vn(e).label}function Om(e){return e==="high"||e==="very_high"}function fo(e){return e==="very_high"}function Gi(e){return Vn(e).gradient||""}function zm(e){return Vn(e).glowColor||""}function $C(){return Object.entries(Qu).map(([e,t])=>({value:e,label:t.label,config:t}))}function UC(e){switch(e){case"very_high":return"h-2";case"high":return"h-1.5";case"medium":return"h-1";case"low":return"h-0.5";case"very_low":return"h-0.5";default:return"h-1"}}function qi(e){return e==="very_high"?"animate-pulse":""}const Xo=({priority:e,variant:t="icon",size:n="md",showLabel:r=!1,className:s="",animated:i=!0})=>{const o=zC(e),a={sm:"w-3 h-3 text-xs",md:"w-4 h-4 text-sm",lg:"w-5 h-5 text-base"},u={sm:"px-1.5 py-0.5 text-xs",md:"px-2 py-1 text-sm",lg:"px-3 py-1.5 text-base"},c=a[n],d=u[n];return t==="icon"?l.jsxs(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${c} ${Fm(e)} ${qi(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="badge"?l.jsxs(P.div,{className:`inline-flex items-center rounded-full ${d} ${Tl(e)} ${Cl(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="bar"?l.jsx(P.div,{className:`w-full ${UC(e)} bg-gradient-to-r ${Gi(e)} rounded-full ${s} ${qi(e)}`,initial:i?{scaleX:0}:void 0,animate:i?{scaleX:1}:void 0,transition:{duration:.3}}):t==="chip"?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} ${Tl(e)} ${Cl(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="full"?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Gi(e)} text-white shadow-lg ${zm(e)} ${s} ${qi(e)}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} text-white`}),l.jsx("span",{className:"ml-1 text-white font-medium",children:On(e)})]}):t==="minimal"?l.jsxs(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx("div",{className:`w-2 h-2 rounded-full bg-gradient-to-r ${Gi(e)} ${qi(e)}`}),r&&l.jsx("span",{className:`ml-2 ${cn(e)} text-sm font-medium`,children:On(e)})]}):t==="glow"?l.jsx(P.div,{className:`inline-flex items-center rounded-lg ${d} ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:fo(e)?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Gi(e)} text-white shadow-lg ${zm(e)} animate-pulse`,animate:{boxShadow:["0 0 20px rgba(239, 68, 68, 0.5)","0 0 30px rgba(239, 68, 68, 0.8)","0 0 20px rgba(239, 68, 68, 0.5)"]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:[l.jsx(o,{className:`${a.sm} text-white`}),r&&l.jsx("span",{className:"ml-1 text-white font-bold",children:On(e)})]}):l.jsxs("div",{className:`inline-flex items-center rounded-lg ${d} ${Tl(e)} ${Cl(e)} border`,children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]})}):l.jsx(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:l.jsx(o,{className:`${c} ${Fm(e)}`})})},BC={pending:"Pending",in_progress:"In Progress",completed:"Completed",archived:"Archived",cancelled:"Cancelled"},HC=[{value:"all",label:"All"},{value:"overdue",label:"Overdue"},{value:"today",label:"Due Today"},{value:"tomorrow",label:"Due Tomorrow"},{value:"this_week",label:"This Week"},{value:"this_month",label:"This Month"},{value:"no_due_date",label:"No Due Date"}],WC=({isOpen:e,onClose:t,filters:n,onUpdateFilter:r,onApplyPreset:s,onClearFilters:i,presets:o,activePreset:a,filterOptions:u,filterSummary:c})=>{const[d,h]=w.useState(new Set(["presets","status","priority","categories"])),f=p=>{h(m=>{const y=new Set(m);return y.has(p)?y.delete(p):y.add(p),y})},g=p=>{const m=n.status.includes(p)?n.status.filter(y=>y!==p):[...n.status,p];r("status",m)},v=p=>{const m=n.priority.includes(p)?n.priority.filter(y=>y!==p):[...n.priority,p];r("priority",m)},x=p=>{const m=n.tags.includes(p)?n.tags.filter(y=>y!==p):[...n.tags,p];r("tags",m)},S=p=>{const m=n.category.includes(p)?n.category.filter(y=>y!==p):[...n.category,p];r("category",m)};return e?l.jsx(ae,{children:l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t,children:l.jsxs(P.div,{initial:{opacity:0,x:300},animate:{opacity:1,x:0},exit:{opacity:0,x:300},transition:{type:"spring",damping:25,stiffness:300},className:"absolute right-0 top-0 h-full w-96 fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto",onClick:p=>p.stopPropagation(),children:[l.jsxs("div",{className:"sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("h2",{className:"fa-heading-2",children:"Filters"}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:l.jsx(St,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"mt-4 p-3 bg-fa-blue-50 rounded-lg",children:l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"text-fa-gray-600",children:["Showing ",c.totalFiltered," of ",c.totalOriginal," tasks"]}),c.hasActiveFilters&&l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:i,className:"flex items-center space-x-1 text-fa-blue-600 hover:text-fa-blue-700",children:[l.jsx(lT,{className:"w-3 h-3"}),l.jsx("span",{children:"Clear"})]})]})})]}),l.jsxs("div",{className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("presets"),className:"flex items-center justify-between w-full text-left",children:[l.jsx("h3",{className:"fa-heading-3",children:"Quick Filters"}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("presets")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("presets")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:o.map(p=>l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s(p.id),className:`w-full text-left p-3 rounded-lg transition-all duration-200 ${a===p.id?"bg-fa-blue-100 border border-fa-blue-300 text-fa-blue-800":"bg-fa-white-glass hover:bg-fa-gray-50 border border-fa-gray-200"}`,children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("span",{className:"text-lg",children:p.icon}),l.jsx("div",{children:l.jsx("div",{className:"font-medium",children:p.name})})]})},p.id))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("status"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Hn,{className:"w-4 h-4 mr-2"}),"Status"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("status")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("status")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.statuses.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>g(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.status.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.status.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ps,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:BC[p]})]},p))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("priority"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(tv,{className:"w-4 h-4 mr-2"}),"Priority"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("priority")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("priority")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.priorities.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>v(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.priority.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.priority.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ps,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx(Xo,{priority:p,variant:"icon",size:"sm",showLabel:!0,animated:!1})]},p))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("dueDate"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Ln,{className:"w-4 h-4 mr-2"}),"Due Date"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("dueDate")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("dueDate")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:HC.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r("dueDate",p.value),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.dueDate===p.value?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.dueDate===p.value?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ps,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:p.label})]},p.value))})})]}),u.categories.length>0&&l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("categories"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Jt,{className:"w-4 h-4 mr-2"}),"Categories"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("categories")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("categories")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.categories.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>S(p.id),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.category.includes(p.id)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.category.includes(p.id)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ps,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:p.color}}),l.jsx("span",{children:p.name})]},p.id))})})]}),u.tags.length>0&&l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("tags"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(mr,{className:"w-4 h-4 mr-2"}),"Tags"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("tags")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("tags")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.tags.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>x(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.tags.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.tags.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ps,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:p})]},p))})})]})]})]})})}):null},$m={title:"Title",status:"Status",priority:"Priority",due_date:"Due Date",created_at:"Created",updated_at:"Updated",position:"Position",estimated_duration:"Duration"},KC=({sortConfig:e,onUpdateSort:t,onApplyPreset:n,onToggleSortOrder:r,presets:s,activePreset:i,sortDescription:o,className:a=""})=>{var p;const[u,c]=w.useState(!1),[d,h]=w.useState(!1),f=w.useRef(null),g=w.useRef(null);w.useEffect(()=>{const m=y=>{f.current&&!f.current.contains(y.target)&&c(!1),g.current&&!g.current.contains(y.target)&&h(!1)};return document.addEventListener("mousedown",m),()=>document.removeEventListener("mousedown",m)},[]);const v=m=>{t(m),c(!1)},x=m=>{n(m),h(!1)},S=()=>{switch(e.order){case"asc":return l.jsx(Xy,{className:"w-4 h-4"});case"desc":return l.jsx(Qy,{className:"w-4 h-4"});default:return l.jsx(Xk,{className:"w-4 h-4"})}};return l.jsxs("div",{className:`flex items-center space-x-2 ${a}`,children:[l.jsxs("div",{className:"relative",ref:g,children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>h(!d),className:`fa-button-glass px-3 py-2 flex items-center space-x-2 ${i?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:[l.jsx("span",{className:"text-sm font-medium",children:i?(p=s.find(m=>m.id===i))==null?void 0:p.name:"Sort by"}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d?"rotate-180":""}`})]}),l.jsx(ae,{children:d&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto",children:l.jsxs("div",{className:"p-2",children:[l.jsx("div",{className:"px-3 py-2 text-xs font-medium text-fa-gray-500 uppercase tracking-wide",children:"Quick Sort Options"}),s.map(m=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>x(m.id),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${i===m.id?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("span",{className:"text-lg",children:m.icon}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium",children:m.name}),m.description&&l.jsx("div",{className:"text-xs text-fa-gray-500",children:m.description})]})]}),i===m.id&&l.jsx(Wo,{className:"w-4 h-4 text-fa-blue-600"})]},m.id))]})})})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsxs("div",{className:"relative",ref:f,children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>c(!u),className:"fa-button-glass px-3 py-2 flex items-center space-x-2",children:[l.jsx("span",{className:"text-sm",children:$m[e.field]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${u?"rotate-180":""}`})]}),l.jsx(ae,{children:u&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-48 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:l.jsx("div",{className:"p-2",children:Object.entries($m).map(([m,y])=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>v(m),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${e.field===m?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsx("span",{children:y}),e.field===m&&l.jsx(Wo,{className:"w-4 h-4 text-fa-blue-600"})]},m))})})})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:r,className:"fa-button-glass p-2 flex items-center justify-center",title:`Sort ${e.order==="asc"?"descending":"ascending"}`,children:S()})]}),l.jsx("div",{className:"hidden md:block",children:l.jsx("span",{className:"text-sm text-fa-gray-500",children:o})})]})},xv=({isOpen:e,onClose:t,onConfirm:n,title:r,message:s,confirmText:i="Confirm",cancelText:o="Cancel",variant:a="danger",isLoading:u=!1})=>{const d=(()=>{switch(a){case"danger":return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"};case"warning":return{icon:"text-fa-warning",confirmButton:"bg-fa-warning hover:bg-yellow-600 text-white",border:"border-fa-warning-border"};case"info":return{icon:"text-fa-info",confirmButton:"bg-fa-info hover:bg-blue-600 text-white",border:"border-fa-info-border"};default:return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"}}})();return l.jsx(ae,{children:e&&l.jsxs(l.Fragment,{children:[l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t}),l.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:l.jsxs(P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted max-w-md w-full p-6 ${d.border}`,onClick:h=>h.stopPropagation(),children:[l.jsxs("div",{className:"flex items-start justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:`p-2 rounded-full bg-fa-white-glass ${d.icon}`,children:l.jsx(Ta,{className:"w-5 h-5"})}),l.jsx("h3",{className:"fa-heading-3 text-fa-gray-800",children:r})]}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-1 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:u,children:l.jsx(St,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"mb-6",children:l.jsx("p",{className:"fa-body text-fa-gray-600",children:s})}),l.jsxs("div",{className:"flex items-center justify-end space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,disabled:u,className:"fa-button-glass px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:o}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:n,disabled:u,className:`px-4 py-2 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${d.confirmButton}`,children:u?l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),l.jsx("span",{children:"Processing..."})]}):i})]})]})})]})})};var ki=e=>e.type==="checkbox",Zn=e=>e instanceof Date,Be=e=>e==null;const wv=e=>typeof e=="object";var be=e=>!Be(e)&&!Array.isArray(e)&&wv(e)&&!Zn(e),Sv=e=>be(e)&&e.target?ki(e.target)?e.target.checked:e.target.value:e,GC=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,bv=(e,t)=>e.has(GC(t)),qC=e=>{const t=e.constructor&&e.constructor.prototype;return be(t)&&t.hasOwnProperty("isPrototypeOf")},Fd=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function je(e){let t;const n=Array.isArray(e),r=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(Fd&&(e instanceof Blob||r))&&(n||be(e)))if(t=n?[]:Object.create(Object.getPrototypeOf(e)),!n&&!qC(e))t=e;else for(const s in e)e.hasOwnProperty(s)&&(t[s]=je(e[s]));else return e;return t}var Aa=e=>/^\w*$/.test(e),we=e=>e===void 0,Od=e=>Array.isArray(e)?e.filter(Boolean):[],zd=e=>Od(e.replace(/["|']|\]/g,"").split(/\.|\[/)),z=(e,t,n)=>{if(!t||!be(e))return n;const r=(Aa(t)?[t]:zd(t)).reduce((s,i)=>Be(s)?s:s[i],e);return we(r)||r===e?we(e[t])?n:e[t]:r},nt=e=>typeof e=="boolean",se=(e,t,n)=>{let r=-1;const s=Aa(t)?[t]:zd(t),i=s.length,o=i-1;for(;++r<i;){const a=s[r];let u=n;if(r!==o){const c=e[a];u=be(c)||Array.isArray(c)?c:isNaN(+s[r+1])?{}:[]}if(a==="__proto__"||a==="constructor"||a==="prototype")return;e[a]=u,e=e[a]}};const Yo={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},_t={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Bt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},kv=Z.createContext(null);kv.displayName="HookFormContext";const $d=()=>Z.useContext(kv);var Tv=(e,t,n,r=!0)=>{const s={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(s,i,{get:()=>{const o=i;return t._proxyFormState[o]!==_t.all&&(t._proxyFormState[o]=!r||_t.all),n&&(n[o]=!0),e[o]}});return s};const Ud=typeof window<"u"?Z.useLayoutEffect:Z.useEffect;function QC(e){const t=$d(),{control:n=t.control,disabled:r,name:s,exact:i}=e||{},[o,a]=Z.useState(n._formState),u=Z.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Ud(()=>n._subscribe({name:s,formState:u.current,exact:i,callback:c=>{!r&&a({...n._formState,...c})}}),[s,r,i]),Z.useEffect(()=>{u.current.isValid&&n._setValid(!0)},[n]),Z.useMemo(()=>Tv(o,n,u.current,!1),[o,n])}var Vt=e=>typeof e=="string",Cv=(e,t,n,r,s)=>Vt(e)?(r&&t.watch.add(e),z(n,e,s)):Array.isArray(e)?e.map(i=>(r&&t.watch.add(i),z(n,i))):(r&&(t.watchAll=!0),n),Xu=e=>Be(e)||!wv(e);function Gt(e,t,n=new WeakSet){if(Xu(e)||Xu(t))return e===t;if(Zn(e)&&Zn(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const i of r){const o=e[i];if(!s.includes(i))return!1;if(i!=="ref"){const a=t[i];if(Zn(o)&&Zn(a)||be(o)&&be(a)||Array.isArray(o)&&Array.isArray(a)?!Gt(o,a,n):o!==a)return!1}}return!0}function XC(e){const t=$d(),{control:n=t.control,name:r,defaultValue:s,disabled:i,exact:o,compute:a}=e||{},u=Z.useRef(s),c=Z.useRef(a),d=Z.useRef(void 0);c.current=a;const h=Z.useMemo(()=>n._getWatch(r,u.current),[n,r]),[f,g]=Z.useState(c.current?c.current(h):h);return Ud(()=>n._subscribe({name:r,formState:{values:!0},exact:o,callback:v=>{if(!i){const x=Cv(r,n._names,v.values||n._formValues,!1,u.current);if(c.current){const S=c.current(x);Gt(S,d.current)||(g(S),d.current=S)}else g(x)}}}),[n,i,r,o]),Z.useEffect(()=>n._removeUnmounted()),f}function YC(e){const t=$d(),{name:n,disabled:r,control:s=t.control,shouldUnregister:i,defaultValue:o}=e,a=bv(s._names.array,n),u=Z.useMemo(()=>z(s._formValues,n,z(s._defaultValues,n,o)),[s,n,o]),c=XC({control:s,name:n,defaultValue:u,exact:!0}),d=QC({control:s,name:n,exact:!0}),h=Z.useRef(e),f=Z.useRef(s.register(n,{...e.rules,value:c,...nt(e.disabled)?{disabled:e.disabled}:{}}));h.current=e;const g=Z.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!z(d.errors,n)},isDirty:{enumerable:!0,get:()=>!!z(d.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!z(d.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!z(d.validatingFields,n)},error:{enumerable:!0,get:()=>z(d.errors,n)}}),[d,n]),v=Z.useCallback(m=>f.current.onChange({target:{value:Sv(m),name:n},type:Yo.CHANGE}),[n]),x=Z.useCallback(()=>f.current.onBlur({target:{value:z(s._formValues,n),name:n},type:Yo.BLUR}),[n,s._formValues]),S=Z.useCallback(m=>{const y=z(s._fields,n);y&&m&&(y._f.ref={focus:()=>m.focus&&m.focus(),select:()=>m.select&&m.select(),setCustomValidity:b=>m.setCustomValidity(b),reportValidity:()=>m.reportValidity()})},[s._fields,n]),p=Z.useMemo(()=>({name:n,value:c,...nt(r)||d.disabled?{disabled:d.disabled||r}:{},onChange:v,onBlur:x,ref:S}),[n,r,d.disabled,v,x,S,c]);return Z.useEffect(()=>{const m=s._options.shouldUnregister||i;s.register(n,{...h.current.rules,...nt(h.current.disabled)?{disabled:h.current.disabled}:{}});const y=(b,C)=>{const j=z(s._fields,b);j&&j._f&&(j._f.mount=C)};if(y(n,!0),m){const b=je(z(s._options.defaultValues,n));se(s._defaultValues,n,b),we(z(s._formValues,n))&&se(s._formValues,n,b)}return!a&&s.register(n),()=>{(a?m&&!s._state.action:m)?s.unregister(n):y(n,!1)}},[n,s,a,i]),Z.useEffect(()=>{s._setDisabledField({disabled:r,name:n})},[r,n,s]),Z.useMemo(()=>({field:p,formState:d,fieldState:g}),[p,d,g])}const Qi=e=>e.render(YC(e));var ZC=(e,t,n,r,s)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:s||!0}}:{},Os=e=>Array.isArray(e)?e:[e],Um=()=>{let e=[];return{get observers(){return e},next:s=>{for(const i of e)i.next&&i.next(s)},subscribe:s=>(e.push(s),{unsubscribe:()=>{e=e.filter(i=>i!==s)}}),unsubscribe:()=>{e=[]}}},Qe=e=>be(e)&&!Object.keys(e).length,Bd=e=>e.type==="file",Et=e=>typeof e=="function",Zo=e=>{if(!Fd)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},jv=e=>e.type==="select-multiple",Hd=e=>e.type==="radio",JC=e=>Hd(e)||ki(e),jl=e=>Zo(e)&&e.isConnected;function ej(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=we(e)?r++:e[t[r++]];return e}function tj(e){for(const t in e)if(e.hasOwnProperty(t)&&!we(e[t]))return!1;return!0}function Te(e,t){const n=Array.isArray(t)?t:Aa(t)?[t]:zd(t),r=n.length===1?e:ej(e,n),s=n.length-1,i=n[s];return r&&delete r[i],s!==0&&(be(r)&&Qe(r)||Array.isArray(r)&&tj(r))&&Te(e,n.slice(0,-1)),e}var Nv=e=>{for(const t in e)if(Et(e[t]))return!0;return!1};function Jo(e,t={}){const n=Array.isArray(e);if(be(e)||n)for(const r in e)Array.isArray(e[r])||be(e[r])&&!Nv(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Jo(e[r],t[r])):Be(e[r])||(t[r]=!0);return t}function _v(e,t,n){const r=Array.isArray(e);if(be(e)||r)for(const s in e)Array.isArray(e[s])||be(e[s])&&!Nv(e[s])?we(t)||Xu(n[s])?n[s]=Array.isArray(e[s])?Jo(e[s],[]):{...Jo(e[s])}:_v(e[s],Be(t)?{}:t[s],n[s]):n[s]=!Gt(e[s],t[s]);return n}var gs=(e,t)=>_v(e,t,Jo(t));const Bm={value:!1,isValid:!1},Hm={value:!0,isValid:!0};var Ev=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!we(e[0].attributes.value)?we(e[0].value)||e[0].value===""?Hm:{value:e[0].value,isValid:!0}:Hm:Bm}return Bm},Pv=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>we(e)?e:t?e===""?NaN:e&&+e:n&&Vt(e)?new Date(e):r?r(e):e;const Wm={isValid:!1,value:null};var Av=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,Wm):Wm;function Km(e){const t=e.ref;return Bd(t)?t.files:Hd(t)?Av(e.refs).value:jv(t)?[...t.selectedOptions].map(({value:n})=>n):ki(t)?Ev(e.refs).value:Pv(we(t.value)?e.ref.value:t.value,e)}var nj=(e,t,n,r)=>{const s={};for(const i of e){const o=z(t,i);o&&se(s,i,o._f)}return{criteriaMode:n,names:[...e],fields:s,shouldUseNativeValidation:r}},ea=e=>e instanceof RegExp,ys=e=>we(e)?e:ea(e)?e.source:be(e)?ea(e.value)?e.value.source:e.value:e,Gm=e=>({isOnSubmit:!e||e===_t.onSubmit,isOnBlur:e===_t.onBlur,isOnChange:e===_t.onChange,isOnAll:e===_t.all,isOnTouch:e===_t.onTouched});const qm="AsyncFunction";var rj=e=>!!e&&!!e.validate&&!!(Et(e.validate)&&e.validate.constructor.name===qm||be(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===qm)),sj=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Qm=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const zs=(e,t,n,r)=>{for(const s of n||Object.keys(e)){const i=z(e,s);if(i){const{_f:o,...a}=i;if(o){if(o.refs&&o.refs[0]&&t(o.refs[0],s)&&!r)return!0;if(o.ref&&t(o.ref,o.name)&&!r)return!0;if(zs(a,t))break}else if(be(a)&&zs(a,t))break}}};function Xm(e,t,n){const r=z(e,n);if(r||Aa(n))return{error:r,name:n};const s=n.split(".");for(;s.length;){const i=s.join("."),o=z(t,i),a=z(e,i);if(o&&!Array.isArray(o)&&n!==i)return{name:n};if(a&&a.type)return{name:i,error:a};if(a&&a.root&&a.root.type)return{name:`${i}.root`,error:a.root};s.pop()}return{name:n}}var ij=(e,t,n,r)=>{n(e);const{name:s,...i}=e;return Qe(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(o=>t[o]===(!r||_t.all))},oj=(e,t,n)=>!e||!t||e===t||Os(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),aj=(e,t,n,r,s)=>s.isOnAll?!1:!n&&s.isOnTouch?!(t||e):(n?r.isOnBlur:s.isOnBlur)?!e:(n?r.isOnChange:s.isOnChange)?e:!0,lj=(e,t)=>!Od(z(e,t)).length&&Te(e,t),uj=(e,t,n)=>{const r=Os(z(e,n));return se(r,"root",t[n]),se(e,n,r),e},ho=e=>Vt(e);function Ym(e,t,n="validate"){if(ho(e)||Array.isArray(e)&&e.every(ho)||nt(e)&&!e)return{type:n,message:ho(e)?e:"",ref:t}}var yr=e=>be(e)&&!ea(e)?e:{value:e,message:""},Zm=async(e,t,n,r,s,i)=>{const{ref:o,refs:a,required:u,maxLength:c,minLength:d,min:h,max:f,pattern:g,validate:v,name:x,valueAsNumber:S,mount:p}=e._f,m=z(n,x);if(!p||t.has(x))return{};const y=a?a[0]:o,b=A=>{s&&y.reportValidity&&(y.setCustomValidity(nt(A)?"":A||""),y.reportValidity())},C={},j=Hd(o),_=ki(o),T=j||_,E=(S||Bd(o))&&we(o.value)&&we(m)||Zo(o)&&o.value===""||m===""||Array.isArray(m)&&!m.length,D=ZC.bind(null,x,r,C),I=(A,R,X,G=Bt.maxLength,H=Bt.minLength)=>{const U=A?R:X;C[x]={type:A?G:H,message:U,ref:o,...D(A?G:H,U)}};if(i?!Array.isArray(m)||!m.length:u&&(!T&&(E||Be(m))||nt(m)&&!m||_&&!Ev(a).isValid||j&&!Av(a).isValid)){const{value:A,message:R}=ho(u)?{value:!!u,message:u}:yr(u);if(A&&(C[x]={type:Bt.required,message:R,ref:y,...D(Bt.required,R)},!r))return b(R),C}if(!E&&(!Be(h)||!Be(f))){let A,R;const X=yr(f),G=yr(h);if(!Be(m)&&!isNaN(m)){const H=o.valueAsNumber||m&&+m;Be(X.value)||(A=H>X.value),Be(G.value)||(R=H<G.value)}else{const H=o.valueAsDate||new Date(m),U=Y=>new Date(new Date().toDateString()+" "+Y),L=o.type=="time",W=o.type=="week";Vt(X.value)&&m&&(A=L?U(m)>U(X.value):W?m>X.value:H>new Date(X.value)),Vt(G.value)&&m&&(R=L?U(m)<U(G.value):W?m<G.value:H<new Date(G.value))}if((A||R)&&(I(!!A,X.message,G.message,Bt.max,Bt.min),!r))return b(C[x].message),C}if((c||d)&&!E&&(Vt(m)||i&&Array.isArray(m))){const A=yr(c),R=yr(d),X=!Be(A.value)&&m.length>+A.value,G=!Be(R.value)&&m.length<+R.value;if((X||G)&&(I(X,A.message,R.message),!r))return b(C[x].message),C}if(g&&!E&&Vt(m)){const{value:A,message:R}=yr(g);if(ea(A)&&!m.match(A)&&(C[x]={type:Bt.pattern,message:R,ref:o,...D(Bt.pattern,R)},!r))return b(R),C}if(v){if(Et(v)){const A=await v(m,n),R=Ym(A,y);if(R&&(C[x]={...R,...D(Bt.validate,R.message)},!r))return b(R.message),C}else if(be(v)){let A={};for(const R in v){if(!Qe(A)&&!r)break;const X=Ym(await v[R](m,n),y,R);X&&(A={...X,...D(R,X.message)},b(X.message),r&&(C[x]=A))}if(!Qe(A)&&(C[x]={ref:y,...A},!r))return C}}return b(!0),C};const cj={mode:_t.onSubmit,reValidateMode:_t.onChange,shouldFocusError:!0};function dj(e={}){let t={...cj,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:Et(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},r={},s=be(t.defaultValues)||be(t.values)?je(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:je(s),o={action:!1,mount:!1,watch:!1},a={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},u,c=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let h={...d};const f={array:Um(),state:Um()},g=t.criteriaMode===_t.all,v=k=>N=>{clearTimeout(c),c=setTimeout(k,N)},x=async k=>{if(!t.disabled&&(d.isValid||h.isValid||k)){const N=t.resolver?Qe((await _()).errors):await E(r,!0);N!==n.isValid&&f.state.next({isValid:N})}},S=(k,N)=>{!t.disabled&&(d.isValidating||d.validatingFields||h.isValidating||h.validatingFields)&&((k||Array.from(a.mount)).forEach(M=>{M&&(N?se(n.validatingFields,M,N):Te(n.validatingFields,M))}),f.state.next({validatingFields:n.validatingFields,isValidating:!Qe(n.validatingFields)}))},p=(k,N=[],M,$,O=!0,F=!0)=>{if($&&M&&!t.disabled){if(o.action=!0,F&&Array.isArray(z(r,k))){const q=M(z(r,k),$.argA,$.argB);O&&se(r,k,q)}if(F&&Array.isArray(z(n.errors,k))){const q=M(z(n.errors,k),$.argA,$.argB);O&&se(n.errors,k,q),lj(n.errors,k)}if((d.touchedFields||h.touchedFields)&&F&&Array.isArray(z(n.touchedFields,k))){const q=M(z(n.touchedFields,k),$.argA,$.argB);O&&se(n.touchedFields,k,q)}(d.dirtyFields||h.dirtyFields)&&(n.dirtyFields=gs(s,i)),f.state.next({name:k,isDirty:I(k,N),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else se(i,k,N)},m=(k,N)=>{se(n.errors,k,N),f.state.next({errors:n.errors})},y=k=>{n.errors=k,f.state.next({errors:n.errors,isValid:!1})},b=(k,N,M,$)=>{const O=z(r,k);if(O){const F=z(i,k,we(M)?z(s,k):M);we(F)||$&&$.defaultChecked||N?se(i,k,N?F:Km(O._f)):X(k,F),o.mount&&x()}},C=(k,N,M,$,O)=>{let F=!1,q=!1;const te={name:k};if(!t.disabled){if(!M||$){(d.isDirty||h.isDirty)&&(q=n.isDirty,n.isDirty=te.isDirty=I(),F=q!==te.isDirty);const re=Gt(z(s,k),N);q=!!z(n.dirtyFields,k),re?Te(n.dirtyFields,k):se(n.dirtyFields,k,!0),te.dirtyFields=n.dirtyFields,F=F||(d.dirtyFields||h.dirtyFields)&&q!==!re}if(M){const re=z(n.touchedFields,k);re||(se(n.touchedFields,k,M),te.touchedFields=n.touchedFields,F=F||(d.touchedFields||h.touchedFields)&&re!==M)}F&&O&&f.state.next(te)}return F?te:{}},j=(k,N,M,$)=>{const O=z(n.errors,k),F=(d.isValid||h.isValid)&&nt(N)&&n.isValid!==N;if(t.delayError&&M?(u=v(()=>m(k,M)),u(t.delayError)):(clearTimeout(c),u=null,M?se(n.errors,k,M):Te(n.errors,k)),(M?!Gt(O,M):O)||!Qe($)||F){const q={...$,...F&&nt(N)?{isValid:N}:{},errors:n.errors,name:k};n={...n,...q},f.state.next(q)}},_=async k=>{S(k,!0);const N=await t.resolver(i,t.context,nj(k||a.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return S(k),N},T=async k=>{const{errors:N}=await _(k);if(k)for(const M of k){const $=z(N,M);$?se(n.errors,M,$):Te(n.errors,M)}else n.errors=N;return N},E=async(k,N,M={valid:!0})=>{for(const $ in k){const O=k[$];if(O){const{_f:F,...q}=O;if(F){const te=a.array.has(F.name),re=O._f&&rj(O._f);re&&d.validatingFields&&S([$],!0);const ft=await Zm(O,a.disabled,i,g,t.shouldUseNativeValidation&&!N,te);if(re&&d.validatingFields&&S([$]),ft[F.name]&&(M.valid=!1,N))break;!N&&(z(ft,F.name)?te?uj(n.errors,ft,F.name):se(n.errors,F.name,ft[F.name]):Te(n.errors,F.name))}!Qe(q)&&await E(q,N,M)}}return M.valid},D=()=>{for(const k of a.unMount){const N=z(r,k);N&&(N._f.refs?N._f.refs.every(M=>!jl(M)):!jl(N._f.ref))&&an(k)}a.unMount=new Set},I=(k,N)=>!t.disabled&&(k&&N&&se(i,k,N),!Gt(Y(),s)),A=(k,N,M)=>Cv(k,a,{...o.mount?i:we(N)?s:Vt(k)?{[k]:N}:N},M,N),R=k=>Od(z(o.mount?i:s,k,t.shouldUnregister?z(s,k,[]):[])),X=(k,N,M={})=>{const $=z(r,k);let O=N;if($){const F=$._f;F&&(!F.disabled&&se(i,k,Pv(N,F)),O=Zo(F.ref)&&Be(N)?"":N,jv(F.ref)?[...F.ref.options].forEach(q=>q.selected=O.includes(q.value)):F.refs?ki(F.ref)?F.refs.forEach(q=>{(!q.defaultChecked||!q.disabled)&&(Array.isArray(O)?q.checked=!!O.find(te=>te===q.value):q.checked=O===q.value||!!O)}):F.refs.forEach(q=>q.checked=q.value===O):Bd(F.ref)?F.ref.value="":(F.ref.value=O,F.ref.type||f.state.next({name:k,values:je(i)})))}(M.shouldDirty||M.shouldTouch)&&C(k,O,M.shouldTouch,M.shouldDirty,!0),M.shouldValidate&&W(k)},G=(k,N,M)=>{for(const $ in N){if(!N.hasOwnProperty($))return;const O=N[$],F=k+"."+$,q=z(r,F);(a.array.has(k)||be(O)||q&&!q._f)&&!Zn(O)?G(F,O,M):X(F,O,M)}},H=(k,N,M={})=>{const $=z(r,k),O=a.array.has(k),F=je(N);se(i,k,F),O?(f.array.next({name:k,values:je(i)}),(d.isDirty||d.dirtyFields||h.isDirty||h.dirtyFields)&&M.shouldDirty&&f.state.next({name:k,dirtyFields:gs(s,i),isDirty:I(k,F)})):$&&!$._f&&!Be(F)?G(k,F,M):X(k,F,M),Qm(k,a)&&f.state.next({...n,name:k}),f.state.next({name:o.mount?k:void 0,values:je(i)})},U=async k=>{o.mount=!0;const N=k.target;let M=N.name,$=!0;const O=z(r,M),F=re=>{$=Number.isNaN(re)||Zn(re)&&isNaN(re.getTime())||Gt(re,z(i,M,re))},q=Gm(t.mode),te=Gm(t.reValidateMode);if(O){let re,ft;const Ti=N.type?Km(O._f):Sv(k),ln=k.type===Yo.BLUR||k.type===Yo.FOCUS_OUT,Bv=!sj(O._f)&&!t.resolver&&!z(n.errors,M)&&!O._f.deps||aj(ln,z(n.touchedFields,M),n.isSubmitted,te,q),Ra=Qm(M,a,ln);se(i,M,Ti),ln?(!N||!N.readOnly)&&(O._f.onBlur&&O._f.onBlur(k),u&&u(0)):O._f.onChange&&O._f.onChange(k);const La=C(M,Ti,ln),Hv=!Qe(La)||Ra;if(!ln&&f.state.next({name:M,type:k.type,values:je(i)}),Bv)return(d.isValid||h.isValid)&&(t.mode==="onBlur"?ln&&x():ln||x()),Hv&&f.state.next({name:M,...Ra?{}:La});if(!ln&&Ra&&f.state.next({...n}),t.resolver){const{errors:Yd}=await _([M]);if(F(Ti),$){const Wv=Xm(n.errors,r,M),Zd=Xm(Yd,r,Wv.name||M);re=Zd.error,M=Zd.name,ft=Qe(Yd)}}else S([M],!0),re=(await Zm(O,a.disabled,i,g,t.shouldUseNativeValidation))[M],S([M]),F(Ti),$&&(re?ft=!1:(d.isValid||h.isValid)&&(ft=await E(r,!0)));$&&(O._f.deps&&W(O._f.deps),j(M,ft,re,La))}},L=(k,N)=>{if(z(n.errors,N)&&k.focus)return k.focus(),1},W=async(k,N={})=>{let M,$;const O=Os(k);if(t.resolver){const F=await T(we(k)?k:O);M=Qe(F),$=k?!O.some(q=>z(F,q)):M}else k?($=(await Promise.all(O.map(async F=>{const q=z(r,F);return await E(q&&q._f?{[F]:q}:q)}))).every(Boolean),!(!$&&!n.isValid)&&x()):$=M=await E(r);return f.state.next({...!Vt(k)||(d.isValid||h.isValid)&&M!==n.isValid?{}:{name:k},...t.resolver||!k?{isValid:M}:{},errors:n.errors}),N.shouldFocus&&!$&&zs(r,L,k?O:a.mount),$},Y=k=>{const N={...o.mount?i:s};return we(k)?N:Vt(k)?z(N,k):k.map(M=>z(N,M))},ce=(k,N)=>({invalid:!!z((N||n).errors,k),isDirty:!!z((N||n).dirtyFields,k),error:z((N||n).errors,k),isValidating:!!z(n.validatingFields,k),isTouched:!!z((N||n).touchedFields,k)}),ke=k=>{k&&Os(k).forEach(N=>Te(n.errors,N)),f.state.next({errors:k?n.errors:{}})},In=(k,N,M)=>{const $=(z(r,k,{_f:{}})._f||{}).ref,O=z(n.errors,k)||{},{ref:F,message:q,type:te,...re}=O;se(n.errors,k,{...re,...N,ref:$}),f.state.next({name:k,errors:n.errors,isValid:!1}),M&&M.shouldFocus&&$&&$.focus&&$.focus()},$t=(k,N)=>Et(k)?f.state.subscribe({next:M=>"values"in M&&k(A(void 0,N),M)}):A(k,N,!0),pr=k=>f.state.subscribe({next:N=>{oj(k.name,N.name,k.exact)&&ij(N,k.formState||d,Uv,k.reRenderRoot)&&k.callback({values:{...i},...n,...N,defaultValues:s})}}).unsubscribe,Ut=k=>(o.mount=!0,h={...h,...k.formState},pr({...k,formState:h})),an=(k,N={})=>{for(const M of k?Os(k):a.mount)a.mount.delete(M),a.array.delete(M),N.keepValue||(Te(r,M),Te(i,M)),!N.keepError&&Te(n.errors,M),!N.keepDirty&&Te(n.dirtyFields,M),!N.keepTouched&&Te(n.touchedFields,M),!N.keepIsValidating&&Te(n.validatingFields,M),!t.shouldUnregister&&!N.keepDefaultValue&&Te(s,M);f.state.next({values:je(i)}),f.state.next({...n,...N.keepDirty?{isDirty:I()}:{}}),!N.keepIsValid&&x()},Kd=({disabled:k,name:N})=>{(nt(k)&&o.mount||k||a.disabled.has(N))&&(k?a.disabled.add(N):a.disabled.delete(N))},Da=(k,N={})=>{let M=z(r,k);const $=nt(N.disabled)||nt(t.disabled);return se(r,k,{...M||{},_f:{...M&&M._f?M._f:{ref:{name:k}},name:k,mount:!0,...N}}),a.mount.add(k),M?Kd({disabled:nt(N.disabled)?N.disabled:t.disabled,name:k}):b(k,!0,N.value),{...$?{disabled:N.disabled||t.disabled}:{},...t.progressive?{required:!!N.required,min:ys(N.min),max:ys(N.max),minLength:ys(N.minLength),maxLength:ys(N.maxLength),pattern:ys(N.pattern)}:{},name:k,onChange:U,onBlur:U,ref:O=>{if(O){Da(k,N),M=z(r,k);const F=we(O.value)&&O.querySelectorAll&&O.querySelectorAll("input,select,textarea")[0]||O,q=JC(F),te=M._f.refs||[];if(q?te.find(re=>re===F):F===M._f.ref)return;se(r,k,{_f:{...M._f,...q?{refs:[...te.filter(jl),F,...Array.isArray(z(s,k))?[{}]:[]],ref:{type:F.type,name:k}}:{ref:F}}}),b(k,!1,void 0,F)}else M=z(r,k,{}),M._f&&(M._f.mount=!1),(t.shouldUnregister||N.shouldUnregister)&&!(bv(a.array,k)&&o.action)&&a.unMount.add(k)}}},Ma=()=>t.shouldFocusError&&zs(r,L,a.mount),Ov=k=>{nt(k)&&(f.state.next({disabled:k}),zs(r,(N,M)=>{const $=z(r,M);$&&(N.disabled=$._f.disabled||k,Array.isArray($._f.refs)&&$._f.refs.forEach(O=>{O.disabled=$._f.disabled||k}))},0,!1))},Gd=(k,N)=>async M=>{let $;M&&(M.preventDefault&&M.preventDefault(),M.persist&&M.persist());let O=je(i);if(f.state.next({isSubmitting:!0}),t.resolver){const{errors:F,values:q}=await _();n.errors=F,O=je(q)}else await E(r);if(a.disabled.size)for(const F of a.disabled)Te(O,F);if(Te(n.errors,"root"),Qe(n.errors)){f.state.next({errors:{}});try{await k(O,M)}catch(F){$=F}}else N&&await N({...n.errors},M),Ma(),setTimeout(Ma);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Qe(n.errors)&&!$,submitCount:n.submitCount+1,errors:n.errors}),$)throw $},zv=(k,N={})=>{z(r,k)&&(we(N.defaultValue)?H(k,je(z(s,k))):(H(k,N.defaultValue),se(s,k,je(N.defaultValue))),N.keepTouched||Te(n.touchedFields,k),N.keepDirty||(Te(n.dirtyFields,k),n.isDirty=N.defaultValue?I(k,je(z(s,k))):I()),N.keepError||(Te(n.errors,k),d.isValid&&x()),f.state.next({...n}))},qd=(k,N={})=>{const M=k?je(k):s,$=je(M),O=Qe(k),F=O?s:$;if(N.keepDefaultValues||(s=M),!N.keepValues){if(N.keepDirtyValues){const q=new Set([...a.mount,...Object.keys(gs(s,i))]);for(const te of Array.from(q))z(n.dirtyFields,te)?se(F,te,z(i,te)):H(te,z(F,te))}else{if(Fd&&we(k))for(const q of a.mount){const te=z(r,q);if(te&&te._f){const re=Array.isArray(te._f.refs)?te._f.refs[0]:te._f.ref;if(Zo(re)){const ft=re.closest("form");if(ft){ft.reset();break}}}}if(N.keepFieldsRef)for(const q of a.mount)H(q,z(F,q));else r={}}i=t.shouldUnregister?N.keepDefaultValues?je(s):{}:je(F),f.array.next({values:{...F}}),f.state.next({values:{...F}})}a={mount:N.keepDirtyValues?a.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!d.isValid||!!N.keepIsValid||!!N.keepDirtyValues,o.watch=!!t.shouldUnregister,f.state.next({submitCount:N.keepSubmitCount?n.submitCount:0,isDirty:O?!1:N.keepDirty?n.isDirty:!!(N.keepDefaultValues&&!Gt(k,s)),isSubmitted:N.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:O?{}:N.keepDirtyValues?N.keepDefaultValues&&i?gs(s,i):n.dirtyFields:N.keepDefaultValues&&k?gs(s,k):N.keepDirty?n.dirtyFields:{},touchedFields:N.keepTouched?n.touchedFields:{},errors:N.keepErrors?n.errors:{},isSubmitSuccessful:N.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:s})},Qd=(k,N)=>qd(Et(k)?k(i):k,N),$v=(k,N={})=>{const M=z(r,k),$=M&&M._f;if($){const O=$.refs?$.refs[0]:$.ref;O.focus&&(O.focus(),N.shouldSelect&&Et(O.select)&&O.select())}},Uv=k=>{n={...n,...k}},Xd={control:{register:Da,unregister:an,getFieldState:ce,handleSubmit:Gd,setError:In,_subscribe:pr,_runSchema:_,_focusError:Ma,_getWatch:A,_getDirty:I,_setValid:x,_setFieldArray:p,_setDisabledField:Kd,_setErrors:y,_getFieldArray:R,_reset:qd,_resetDefaultValues:()=>Et(t.defaultValues)&&t.defaultValues().then(k=>{Qd(k,t.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:D,_disableForm:Ov,_subjects:f,_proxyFormState:d,get _fields(){return r},get _formValues(){return i},get _state(){return o},set _state(k){o=k},get _defaultValues(){return s},get _names(){return a},set _names(k){a=k},get _formState(){return n},get _options(){return t},set _options(k){t={...t,...k}}},subscribe:Ut,trigger:W,register:Da,handleSubmit:Gd,watch:$t,setValue:H,getValues:Y,reset:Qd,resetField:zv,clearErrors:ke,unregister:an,setError:In,setFocus:$v,getFieldState:ce};return{...Xd,formControl:Xd}}function Dv(e={}){const t=Z.useRef(void 0),n=Z.useRef(void 0),[r,s]=Z.useState({isDirty:!1,isValidating:!1,isLoading:Et(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Et(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:r},e.defaultValues&&!Et(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:o,...a}=dj(e);t.current={...a,formState:r}}const i=t.current.control;return i._options=e,Ud(()=>{const o=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(a=>({...a,isReady:!0})),i._formState.isReady=!0,o},[i]),Z.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),Z.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),Z.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),Z.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),Z.useEffect(()=>{if(i._proxyFormState.isDirty){const o=i._getDirty();o!==r.isDirty&&i._subjects.state.next({isDirty:o})}},[i,r.isDirty]),Z.useEffect(()=>{e.values&&!Gt(e.values,n.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),n.current=e.values,s(o=>({...o}))):i._resetDefaultValues()},[i,e.values]),Z.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=Tv(r,i),t.current}const We={TITLE:{MIN_LENGTH:1,MAX_LENGTH:500},DESCRIPTION:{MAX_LENGTH:5e3},TAGS:{MAX_COUNT:20,MAX_TAG_LENGTH:50,MIN_TAG_LENGTH:1},ESTIMATED_DURATION:{MAX_HOURS:999,MIN_MINUTES:1}},Jm=["pending","in_progress","completed","archived","cancelled"],ep=["very_low","low","medium","high","very_high"];function rr(e){return e?e.trim().replace(/\s+/g," "):""}function fj(e){const t=[],n=rr(e);return n?(n.length<We.TITLE.MIN_LENGTH&&t.push({field:"title",message:`Title must be at least ${We.TITLE.MIN_LENGTH} character long`,code:"TITLE_TOO_SHORT",value:e}),n.length>We.TITLE.MAX_LENGTH&&t.push({field:"title",message:`Title cannot exceed ${We.TITLE.MAX_LENGTH} characters`,code:"TITLE_TOO_LONG",value:e}),t):(t.push({field:"title",message:"Title is required",code:"TITLE_REQUIRED",value:e}),t)}function hj(e){const t=[],n=rr(e);return n&&n.length>We.DESCRIPTION.MAX_LENGTH&&t.push({field:"description",message:`Description cannot exceed ${We.DESCRIPTION.MAX_LENGTH} characters`,code:"DESCRIPTION_TOO_LONG",value:e}),t}function mj(e){const t=[];return e&&!Jm.includes(e)&&t.push({field:"status",message:`Invalid status. Must be one of: ${Jm.join(", ")}`,code:"INVALID_STATUS",value:e}),t}function pj(e){const t=[];return e&&!ep.includes(e)&&t.push({field:"priority",message:`Invalid priority. Must be one of: ${ep.join(", ")}`,code:"INVALID_PRIORITY",value:e}),t}function gj(e,t=!0){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"due_date",message:"Invalid date format",code:"INVALID_DATE_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"due_date",message:"Invalid date",code:"INVALID_DATE",value:e}),n;!t&&r<new Date&&n.push({field:"due_date",message:"Due date cannot be in the past",code:"DUE_DATE_IN_PAST",value:e});const s=new Date;return s.setFullYear(s.getFullYear()+10),r>s&&n.push({field:"due_date",message:"Due date is too far in the future",code:"DUE_DATE_TOO_FAR",value:e}),n}function yj(e,t){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"reminder_at",message:"Invalid reminder date format",code:"INVALID_REMINDER_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"reminder_at",message:"Invalid reminder date",code:"INVALID_REMINDER_DATE",value:e}),n;if(r<new Date&&n.push({field:"reminder_at",message:"Reminder date cannot be in the past",code:"REMINDER_IN_PAST",value:e}),t){const s=typeof t=="string"?new Date(t):t;!isNaN(s.getTime())&&r>s&&n.push({field:"reminder_at",message:"Reminder date cannot be after due date",code:"REMINDER_AFTER_DUE_DATE",value:e})}return n}function vj(e){const t=[];return e?Array.isArray(e)?(e.length>We.TAGS.MAX_COUNT&&t.push({field:"tags",message:`Cannot have more than ${We.TAGS.MAX_COUNT} tags`,code:"TOO_MANY_TAGS",value:e}),e.forEach((r,s)=>{if(typeof r!="string"){t.push({field:`tags[${s}]`,message:"Tag must be a string",code:"TAG_NOT_STRING",value:r});return}const i=rr(r);i.length<We.TAGS.MIN_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:"Tag cannot be empty",code:"TAG_EMPTY",value:r}),i.length>We.TAGS.MAX_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:`Tag cannot exceed ${We.TAGS.MAX_TAG_LENGTH} characters`,code:"TAG_TOO_LONG",value:r})}),new Set(e.map(r=>rr(r).toLowerCase())).size!==e.length&&t.push({field:"tags",message:"Duplicate tags are not allowed",code:"DUPLICATE_TAGS",value:e}),t):(t.push({field:"tags",message:"Tags must be an array",code:"TAGS_NOT_ARRAY",value:e}),t):t}function xj(e){const t=[];if(!e)return t;const n=/^PT(?:(\d+)H)?(?:(\d+)M)?$/,r=e.match(n);if(!r)return t.push({field:"estimated_duration",message:"Invalid duration format. Use format like PT1H30M (1 hour 30 minutes)",code:"INVALID_DURATION_FORMAT",value:e}),t;const s=parseInt(r[1]||"0",10),i=parseInt(r[2]||"0",10);return s>We.ESTIMATED_DURATION.MAX_HOURS&&t.push({field:"estimated_duration",message:`Duration cannot exceed ${We.ESTIMATED_DURATION.MAX_HOURS} hours`,code:"DURATION_TOO_LONG",value:e}),s===0&&i<We.ESTIMATED_DURATION.MIN_MINUTES&&t.push({field:"estimated_duration",message:`Duration must be at least ${We.ESTIMATED_DURATION.MIN_MINUTES} minute`,code:"DURATION_TOO_SHORT",value:e}),t}function wj(e){const t=[];return e?(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"user_id",message:"Invalid user ID format",code:"INVALID_USER_ID_FORMAT",value:e}),t):(t.push({field:"user_id",message:"User ID is required",code:"USER_ID_REQUIRED",value:e}),t)}function Sj(e){const t=[];return e&&(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"category_id",message:"Invalid category ID format",code:"INVALID_CATEGORY_ID_FORMAT",value:e})),t}function Nl(e){const t={...e};return t.title!==void 0&&(t.title=rr(t.title)),t.description!==void 0&&(t.description=rr(t.description)||void 0),t.tags&&(t.tags=t.tags.map(n=>rr(n)).filter(n=>n.length>0).filter((n,r,s)=>s.indexOf(n)===r)),t.metadata===void 0?t.metadata={}:(typeof t.metadata!="object"||t.metadata===null)&&(t.metadata={}),t}function tp(e,t={}){const{isCreate:n=!1,allowPastDueDates:r=!0,requireUserId:s=!0}=t,i=[];return(n||e.title!==void 0)&&i.push(...fj(e.title)),s&&(n||e.user_id!==void 0)&&i.push(...wj(e.user_id)),e.description!==void 0&&i.push(...hj(e.description)),e.status!==void 0&&i.push(...mj(e.status)),e.priority!==void 0&&i.push(...pj(e.priority)),e.due_date!==void 0&&i.push(...gj(e.due_date,r)),e.reminder_at!==void 0&&i.push(...yj(e.reminder_at,e.due_date)),e.tags!==void 0&&i.push(...vj(e.tags)),e.estimated_duration!==void 0&&i.push(...xj(e.estimated_duration)),e.category_id!==void 0&&i.push(...Sj(e.category_id)),{isValid:i.length===0,errors:i}}function bj(e,t){return e.filter(n=>n.field===t||n.field.startsWith(`${t}[`))}function kj(e={}){const[t,n]=w.useState({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1}),r=w.useCallback(h=>{const f={};h.errors.forEach(g=>{const v=g.field;f[v]||(f[v]=[]),f[v].push(g)}),n({isValid:h.isValid,errors:h.errors,fieldErrors:f,hasErrors:h.errors.length>0,isValidating:!1})},[]),s=w.useCallback(h=>{n(v=>({...v,isValidating:!0}));const f=Nl(h),g=tp(f,e);return r(g),g},[e,r]),i=w.useCallback((h,f,g={})=>{const v={...g,[h]:f},x=Nl(v),S=tp(x,{...e,isCreate:!1});return bj(S.errors,h)},[e]),o=w.useCallback(()=>{n({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1})},[]),a=w.useCallback(h=>{n(f=>{const g={...f.fieldErrors};delete g[h];const v=f.errors.filter(x=>!x.field.startsWith(h)&&x.field!==h);return{...f,errors:v,fieldErrors:g,hasErrors:v.length>0,isValid:v.length===0}})},[]),u=w.useCallback(h=>{const f=t.fieldErrors[h];return!f||f.length===0?null:f.length===1?f[0].message:f.map(g=>g.message).join(", ")},[t.fieldErrors]),c=w.useCallback(h=>{var f;return((f=t.fieldErrors[h])==null?void 0:f.length)>0},[t.fieldErrors]),d=w.useCallback(h=>Nl(h),[]);return{validationState:t,validateData:s,validateField:i,clearValidation:o,clearFieldErrors:a,getFieldErrorMessage:u,hasFieldError:c,sanitizeData:d}}const Tj=({toast:e,onDismiss:t})=>{const[n,r]=w.useState(!0);w.useEffect(()=>{if(e.duration&&e.duration>0){const u=setTimeout(()=>{r(!1),setTimeout(()=>t(e.id),300)},e.duration);return()=>clearTimeout(u)}},[e.duration,e.id,t]);const s=()=>{r(!1),setTimeout(()=>t(e.id),300)},i=()=>{switch(e.type){case"success":return l.jsx(Hn,{className:"w-5 h-5 text-green-500"});case"error":return l.jsx(Cd,{className:"w-5 h-5 text-red-500"});case"warning":return l.jsx(Ta,{className:"w-5 h-5 text-yellow-500"});case"info":return l.jsx(jm,{className:"w-5 h-5 text-blue-500"});default:return l.jsx(jm,{className:"w-5 h-5 text-blue-500"})}},o=()=>{switch(e.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-blue-50 border-blue-200"}},a=()=>{switch(e.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-blue-800"}};return l.jsx(ae,{children:n&&l.jsx(P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3,ease:"easeOut"},className:`
            relative max-w-sm w-full border rounded-lg shadow-lg backdrop-blur-sm
            ${o()}
          `,children:l.jsx("div",{className:"p-4",children:l.jsxs("div",{className:"flex items-start space-x-3",children:[l.jsx("div",{className:"flex-shrink-0",children:i()}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h4",{className:`text-sm font-medium ${a()}`,children:e.title}),e.message&&l.jsx("p",{className:`mt-1 text-sm ${a()} opacity-80`,children:e.message}),e.action&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:e.action.onClick,className:`
                        text-sm font-medium underline hover:no-underline
                        ${a()}
                      `,children:e.action.label})})]}),l.jsx("button",{onClick:s,className:`
                  flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10
                  ${a()} opacity-60 hover:opacity-100
                `,children:l.jsx(St,{className:"w-4 h-4"})})]})})})})},Cj=({toasts:e,onDismiss:t,position:n="top-right"})=>{const r=()=>{switch(n){case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-center":return"top-4 left-1/2 transform -translate-x-1/2";case"bottom-center":return"bottom-4 left-1/2 transform -translate-x-1/2";default:return"top-4 right-4"}};return l.jsx("div",{className:`fixed z-50 ${r()}`,children:l.jsx("div",{className:"space-y-3",children:l.jsx(ae,{children:e.map(s=>l.jsx(Tj,{toast:s,onDismiss:t},s.id))})})})};function Mv(){const[e,t]=w.useState([]),n=w.useCallback(c=>{const d=Math.random().toString(36).substr(2,9),h={...c,id:d,duration:c.duration??5e3};return t(f=>[...f,h]),d},[]),r=w.useCallback(c=>{t(d=>d.filter(h=>h.id!==c))},[]),s=w.useCallback(()=>{t([])},[]),i=w.useCallback((c,d,h)=>n({...h,type:"success",title:c,message:d}),[n]),o=w.useCallback((c,d,h)=>n({...h,type:"error",title:c,message:d,duration:(h==null?void 0:h.duration)??7e3}),[n]),a=w.useCallback((c,d,h)=>n({...h,type:"warning",title:c,message:d}),[n]),u=w.useCallback((c,d,h)=>n({...h,type:"info",title:c,message:d}),[n]);return{toasts:e,addToast:n,dismissToast:r,dismissAll:s,success:i,error:o,warning:a,info:u}}const jj={folder:Jt,tag:mr,star:Pd,heart:jd,zap:Ad,home:Nd,briefcase:Zy,"shopping-cart":iv,calendar:Ln,book:Yy,music:rv,camera:Jy,coffee:ev},Nj=({categories:e,selectedCategoryId:t,onSelect:n,onCreateNew:r,placeholder:s="Select a category...",disabled:i=!1,className:o=""})=>{const[a,u]=w.useState(!1),[c,d]=w.useState(""),h=w.useRef(null),f=w.useRef(null),g=e.find(y=>y.id===t),v=e.filter(y=>y.name.toLowerCase().includes(c.toLowerCase()));w.useEffect(()=>{const y=b=>{h.current&&!h.current.contains(b.target)&&(u(!1),d(""))};return document.addEventListener("mousedown",y),()=>document.removeEventListener("mousedown",y)},[]),w.useEffect(()=>{a&&f.current&&f.current.focus()},[a]);const x=()=>{i||(u(!a),d(""))},S=y=>{n(y),u(!1),d("")},p=()=>{r&&(r(),u(!1),d(""))},m=y=>(y?jj[y]:Jt)||Jt;return l.jsxs("div",{className:`relative ${o}`,ref:h,children:[l.jsxs(P.button,{type:"button",whileHover:i?{}:{scale:1.01},whileTap:i?{}:{scale:.99},onClick:x,disabled:i,className:`w-full flex items-center justify-between p-3 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 text-fa-gray-400 cursor-not-allowed":a?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-300 bg-white hover:border-fa-gray-400"}`,children:[l.jsx("div",{className:"flex items-center space-x-3",children:g?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:g.color},children:Z.createElement(m(g.icon),{className:"w-3 h-3 text-white"})}),l.jsx("span",{className:"text-fa-gray-800",children:g.name})]}):l.jsxs(l.Fragment,{children:[l.jsx(Jt,{className:"w-5 h-5 text-fa-gray-400"}),l.jsx("span",{className:"text-fa-gray-500",children:s})]})}),l.jsxs("div",{className:"flex items-center space-x-2",children:[g&&l.jsx(P.button,{type:"button",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y=>{y.stopPropagation(),S(void 0)},className:"p-1 rounded text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(St,{className:"w-3 h-3"})}),l.jsx(Kt,{className:`w-4 h-4 text-fa-gray-400 transition-transform duration-200 ${a?"rotate-180":""}`})]})]}),l.jsx(ae,{children:a&&l.jsxs(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden",children:[l.jsx("div",{className:"p-3 border-b border-fa-gray-100",children:l.jsxs("div",{className:"relative",children:[l.jsx(zu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400"}),l.jsx("input",{ref:f,type:"text",value:c,onChange:y=>d(y.target.value),placeholder:"Search categories...",className:"w-full pl-10 pr-4 py-2 border border-fa-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-fa-blue-400 focus:border-transparent"})]})}),l.jsxs("div",{className:"max-h-48 overflow-y-auto",children:[l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(void 0),className:"w-full flex items-center space-x-3 p-3 text-left hover:bg-fa-gray-50 transition-colors duration-150",children:[l.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-gray-300 flex items-center justify-center",children:l.jsx(St,{className:"w-3 h-3 text-fa-gray-400"})}),l.jsx("span",{className:"text-fa-gray-500 italic",children:"No category"})]}),v.map(y=>{const b=m(y.icon);return l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(y.id),className:`w-full flex items-center space-x-3 p-3 text-left transition-colors duration-150 ${t===y.id?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[l.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:y.color},children:l.jsx(b,{className:"w-3 h-3 text-white"})}),l.jsx("span",{className:"flex-1",children:y.name}),y.is_default&&l.jsx(Pd,{className:"w-3 h-3 text-fa-yellow-500"})]},y.id)}),c&&v.length===0&&l.jsx("div",{className:"p-3 text-center text-fa-gray-500 text-sm",children:"No categories found"}),r&&l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(59, 130, 246, 0.05)"},onClick:p,className:"w-full flex items-center space-x-3 p-3 text-left border-t border-fa-gray-100 text-fa-blue-600 hover:bg-fa-blue-50 transition-colors duration-150",children:[l.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-blue-300 flex items-center justify-center",children:l.jsx(ka,{className:"w-3 h-3"})}),l.jsx("span",{children:"Create new category"})]})]})]})})]})},_j=({tags:e,onChange:t,suggestions:n=[],placeholder:r="Add a tag...",maxTags:s=10,disabled:i=!1,className:o="",enableSmartSuggestions:a=!0})=>{const[u,c]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(-1),[v,x]=w.useState([]),[S,p]=w.useState(!1),m=w.useRef(null),y=w.useRef(null),b=w.useRef(null),C=w.useCallback(async G=>{if(a){p(!0);try{const H=await Cs.getAutocompleteSuggestions(G,e,5);x(H)}catch(H){console.error("Error fetching smart suggestions:",H),x([])}finally{p(!1)}}},[e,a]),_=(a?v:n).filter(G=>G.toLowerCase().includes(u.toLowerCase())&&!e.includes(G)).slice(0,5),T=G=>{const H=G.target.value;c(H),g(-1),b.current&&clearTimeout(b.current),a&&(b.current=setTimeout(()=>{C(H)},300));const U=!a&&n.length>0,L=a&&v.length>0;h(H.length>0&&(U||L))},E=G=>{if(!i)switch(G.key){case"Enter":case",":G.preventDefault(),f>=0&&_[f]?D(_[f]):u.trim()&&D(u.trim());break;case"ArrowDown":G.preventDefault(),d&&g(H=>H<_.length-1?H+1:H);break;case"ArrowUp":G.preventDefault(),d&&g(H=>H>0?H-1:-1);break;case"Escape":h(!1),g(-1);break;case"Backspace":u===""&&e.length>0&&I(e[e.length-1]);break}},D=G=>{const H=G.trim().toLowerCase();H&&!e.includes(H)&&e.length<s&&t([...e,H]),c(""),h(!1),g(-1)},I=G=>{t(e.filter(H=>H!==G))},A=G=>{D(G)},R=()=>{setTimeout(()=>{h(!1),g(-1)},150)},X=()=>{i||(a&&u.length===0&&C(""),(u.length>0&&_.length>0||a)&&h(!0))};return w.useEffect(()=>()=>{b.current&&clearTimeout(b.current)},[]),w.useEffect(()=>{a&&C("")},[C,a]),l.jsxs("div",{className:`relative ${o}`,children:[l.jsx("div",{className:`min-h-[42px] p-2 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 cursor-not-allowed":"bg-white border-fa-gray-300 hover:border-fa-gray-400 focus-within:border-fa-blue-400 focus-within:ring-2 focus-within:ring-fa-blue-400 focus-within:ring-opacity-20"}`,children:l.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[l.jsx(ae,{children:e.map((G,H)=>l.jsxs(P.span,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"inline-flex items-center px-2 py-1 rounded-full text-sm bg-fa-blue-100 text-fa-blue-800",children:[l.jsx(Cm,{className:"w-3 h-3 mr-1"}),G,!i&&l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},type:"button",onClick:()=>I(G),className:"ml-1 text-fa-blue-600 hover:text-fa-blue-800",children:l.jsx(St,{className:"w-3 h-3"})})]},G))}),e.length<s&&l.jsx("input",{ref:m,type:"text",value:u,onChange:T,onKeyDown:E,onBlur:R,onFocus:X,disabled:i,placeholder:e.length===0?r:"",className:"flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder-fa-gray-400 disabled:cursor-not-allowed"}),u.trim()&&!i&&l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:()=>D(u.trim()),className:"p-1 rounded text-fa-blue-600 hover:bg-fa-blue-100",children:l.jsx(ka,{className:"w-4 h-4"})})]})}),l.jsx(ae,{children:d&&(_.length>0||S)&&l.jsx(P.div,{ref:y,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-1 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-40 overflow-y-auto",children:S?l.jsxs("div",{className:"flex items-center justify-center px-3 py-2",children:[l.jsx(nT,{className:"w-4 h-4 animate-spin text-fa-gray-400 mr-2"}),l.jsx("span",{className:"text-sm text-fa-gray-500",children:"Loading suggestions..."})]}):_.map((G,H)=>l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>A(G),className:`w-full flex items-center space-x-2 px-3 py-2 text-left transition-colors duration-150 ${H===f?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"} ${H===0?"rounded-t-lg":""} ${H===_.length-1?"rounded-b-lg":""}`,children:[l.jsx(Cm,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:G})]},G))})}),e.length>=s&&l.jsxs("p",{className:"text-xs text-fa-gray-500 mt-1",children:["Maximum ",s," tags allowed"]})]})},Ej=["#007bff","#28a745","#dc3545","#ffc107","#6f42c1","#fd7e14","#20c997","#e83e8c","#6c757d","#17a2b8","#343a40","#f8f9fa","#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#ffeaa7","#dda0dd","#98d8c8","#f7dc6f","#bb8fce","#85c1e9","#f8c471","#82e0aa"],Pj=()=>{const e=Math.floor(Math.random()*360),t=Math.floor(Math.random()*40)+60,n=Math.floor(Math.random()*30)+40;return((s,i,o)=>{o/=100;const a=i*Math.min(o,1-o)/100,u=c=>{const d=(c+s/30)%12,h=o-a*Math.max(Math.min(d-3,9-d,1),-1);return Math.round(255*h).toString(16).padStart(2,"0")};return`#${u(0)}${u(8)}${u(4)}`})(e,t,n)},Aj=({selectedColor:e,onColorSelect:t,className:n=""})=>{const[r,s]=w.useState(e),[i,o]=w.useState(!1),a=h=>{t(h),s(h)},u=h=>{const f=h.target.value;s(f),t(f)},c=()=>{const h=Pj();t(h),s(h)},d=h=>h===e;return l.jsxs("div",{className:`space-y-4 ${n}`,children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Choose a color"}),l.jsx("div",{className:"grid grid-cols-8 gap-2",children:Ej.map(h=>l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>a(h),className:`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${d(h)?"border-fa-gray-800 shadow-lg":"border-fa-gray-200 hover:border-fa-gray-400"}`,style:{backgroundColor:h},title:h,children:l.jsx(ae,{children:d(h)&&l.jsx(P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},className:"w-full h-full flex items-center justify-center",children:l.jsx(Wo,{className:"w-4 h-4 text-white drop-shadow-sm"})})})},h))})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:c,className:"flex items-center space-x-2 px-3 py-2 bg-fa-blue-100 text-fa-blue-700 rounded-lg hover:bg-fa-blue-200 transition-colors",children:[l.jsx(sv,{className:"w-4 h-4"}),l.jsx("span",{children:"Random"})]}),l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>o(!i),className:"flex items-center space-x-2 px-3 py-2 bg-fa-gray-100 text-fa-gray-700 rounded-lg hover:bg-fa-gray-200 transition-colors",children:[l.jsx(oT,{className:"w-4 h-4"}),l.jsx("span",{children:"Custom"})]})]}),l.jsx(ae,{children:i&&l.jsxs(P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700",children:"Custom color"}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{type:"color",value:r,onChange:u,className:"w-12 h-10 border border-fa-gray-300 rounded-lg cursor-pointer"}),l.jsx("input",{type:"text",value:r,onChange:h=>{const f=h.target.value;s(f),/^#[0-9A-F]{6}$/i.test(f)&&t(f)},placeholder:"#007bff",className:"flex-1 px-3 py-2 border border-fa-gray-300 rounded-lg focus:ring-2 focus:ring-fa-blue-500 focus:border-transparent"})]}),l.jsx("p",{className:"text-xs text-fa-gray-500",children:"Enter a hex color code (e.g., #007bff) or use the color picker"})]})}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-fa-gray-50 rounded-lg",children:[l.jsx("div",{className:"w-6 h-6 rounded border border-fa-gray-300",style:{backgroundColor:e}}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-fa-gray-800",children:"Selected Color"}),l.jsx("p",{className:"text-xs text-fa-gray-500",children:e})]})]})]})},np=[{icon:Jt,name:"folder"},{icon:mr,name:"tag"},{icon:Pd,name:"star"},{icon:jd,name:"heart"},{icon:Ad,name:"zap"},{icon:Nd,name:"home"},{icon:Zy,name:"briefcase"},{icon:iv,name:"shopping-cart"},{icon:Ln,name:"calendar"},{icon:Yy,name:"book"},{icon:rv,name:"music"},{icon:Jy,name:"camera"},{icon:ev,name:"coffee"}],Dj=({isOpen:e,onClose:t,onSubmit:n,category:r,mode:s})=>{const[i,o]=w.useState(!1),[a,u]=w.useState(null),{register:c,handleSubmit:d,control:h,watch:f,setValue:g,reset:v,formState:{errors:x,isValid:S}}=Dv({defaultValues:{name:"",color:"#007bff",icon:"folder",is_default:!1},mode:"onChange"}),p=f("color"),m=f("icon");w.useEffect(()=>{e&&(v(s==="edit"&&r?{name:r.name,color:r.color,icon:r.icon||"folder",is_default:r.is_default}:{name:"",color:"#007bff",icon:"folder",is_default:!1}),u(null))},[e,s,r,v]);const y=async j=>{o(!0),u(null);try{await n({name:j.name.trim(),color:j.color,icon:j.icon,is_default:j.is_default}),t()}catch(_){const T=_ instanceof Error?_.message:"An error occurred";u(T)}finally{o(!1)}},b=()=>{const j=np.find(_=>_.name===m);return j?j.icon:Jt};if(!e)return null;const C=b();return l.jsx(ae,{children:l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"fa-glass-panel w-full max-w-md max-h-[90vh] overflow-y-auto",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-white-glass",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{backgroundColor:p},children:l.jsx(C,{className:"w-4 h-4 text-white"})}),l.jsx("h2",{className:"fa-h3 text-fa-gray-800",children:s==="create"?"Create Category":"Edit Category"})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:t,className:"p-2 rounded-lg text-fa-gray-500 hover:bg-fa-white-glass",children:l.jsx(St,{className:"w-5 h-5"})})]}),l.jsxs("form",{onSubmit:d(y),className:"p-6 space-y-6",children:[a&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"p-4 bg-fa-error bg-opacity-10 border border-fa-error rounded-lg",children:l.jsx("p",{className:"text-fa-error text-sm",children:a})}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Category Name"}),l.jsx("input",{...c("name",{required:"Category name is required",minLength:{value:1,message:"Name must be at least 1 character"},maxLength:{value:50,message:"Name must be less than 50 characters"}}),className:"fa-input w-full",placeholder:"Enter category name...",autoFocus:!0}),x.name&&l.jsx("p",{className:"text-fa-error text-sm mt-1",children:x.name.message})]}),l.jsx(Aj,{selectedColor:p,onColorSelect:j=>g("color",j)}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Icon"}),l.jsx("div",{className:"grid grid-cols-6 gap-2",children:np.map(({icon:j,name:_})=>l.jsx(P.button,{type:"button",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>g("icon",_),className:`p-3 rounded-lg border transition-all duration-200 flex items-center justify-center ${m===_?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-200 hover:border-fa-gray-400 hover:bg-fa-gray-50"}`,children:l.jsx(j,{className:"w-5 h-5 text-fa-gray-600"})},_))})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{...c("is_default"),type:"checkbox",id:"is_default",className:"w-4 h-4 text-fa-blue-600 bg-fa-gray-100 border-fa-gray-300 rounded focus:ring-fa-blue-500"}),l.jsx("label",{htmlFor:"is_default",className:"text-sm text-fa-gray-700",children:"Set as default category"})]}),l.jsxs("div",{className:"flex space-x-3 pt-4",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"flex-1 fa-button-secondary",children:"Cancel"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:!S||i,className:"flex-1 fa-button-primary disabled:opacity-50 disabled:cursor-not-allowed",children:i?l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),l.jsx("span",{children:"Saving..."})]}):l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(Ed,{className:"w-4 h-4"}),l.jsx("span",{children:s==="create"?"Create":"Save"})]})})]})]})]})})})},Mj=$C(),Rj=[{value:"pending",label:"Pending",color:"text-fa-gray-500"},{value:"in_progress",label:"In Progress",color:"text-fa-blue-500"},{value:"completed",label:"Completed",color:"text-fa-green-500"},{value:"archived",label:"Archived",color:"text-fa-gray-400"},{value:"cancelled",label:"Cancelled",color:"text-fa-red-500"}],Rv=({isOpen:e,onClose:t,onSubmit:n,todo:r,mode:s})=>{var D,I;const[i,o]=w.useState(!1),[a,u]=w.useState(null),[c,d]=w.useState(!1),h=kj({isCreate:s==="create",allowPastDueDates:!0,requireUserId:!1}),f=Mv(),{categories:g,getAllTags:v,createCategory:x,loadCategories:S}=Pa(),{register:p,handleSubmit:m,control:y,reset:b,formState:{errors:C,isValid:j}}=Dv({defaultValues:{title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0},mode:"onChange"});w.useEffect(()=>{if(e){if(s==="edit"&&r){const A=r.due_date?new Date(r.due_date).toISOString().split("T")[0]:"",R=r.reminder_at?new Date(r.reminder_at).toISOString().slice(0,16):"";let X=0,G=0;if(r.estimated_duration){const H=r.estimated_duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);H&&(X=parseInt(H[1]||"0",10),G=parseInt(H[2]||"0",10))}b({title:r.title,description:r.description||"",priority:r.priority,status:r.status,category_id:r.category_id,due_date:A,reminder_at:R,tags:r.tags,estimated_duration_hours:X,estimated_duration_minutes:G})}else b({title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0});u(null)}},[e,s,r,b]);const _=(A,R)=>A===0&&R===0?"":`PT${A>0?`${A}H`:""}${R>0?`${R}M`:""}`,T=async A=>{o(!0),u(null);try{const R=_(A.estimated_duration_hours,A.estimated_duration_minutes),X={title:A.title.trim(),description:A.description.trim()||void 0,priority:A.priority,status:A.status,category_id:A.category_id||void 0,due_date:A.due_date?new Date(A.due_date):void 0,tags:A.tags,estimated_duration:R||void 0},G=h.validateData(X);if(!G.isValid){const H=G.errors.length===1?G.errors[0].message:`Please fix ${G.errors.length} validation errors`;u(H),f.error("Validation Error",H);return}if(s==="create"){const H=X,U=await He.createTodo(H);f.success("Todo Created","Your todo has been created successfully"),n(U)}else{if(!r)throw new Error("Todo is required for edit mode");const H=X,U=await He.updateTodo(r.id,H);f.success("Todo Updated","Your todo has been updated successfully"),n(U)}t()}catch(R){const X=R instanceof Error?R.message:"An error occurred";u(X),X.includes("validation")?f.error("Validation Error",X):X.includes("not found")?f.error("Todo Not Found","The todo you are trying to edit no longer exists"):f.error("Operation Failed",X)}finally{o(!1)}},E=async A=>{try{await x(A),await S(),f.success("Category Created",`"${A.name}" has been created successfully`)}catch(R){const X=R instanceof Error?R.message:"Failed to create category";throw f.error("Create Category Failed",X),R}};return e?l.jsxs(l.Fragment,{children:[l.jsx(ae,{children:e&&l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center z-50 p-4",onClick:t,children:l.jsxs(P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"fa-glass-panel-frosted w-full max-w-2xl max-h-[90vh] overflow-y-auto",onClick:A=>A.stopPropagation(),children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-gray-200",children:[l.jsx("h2",{className:"fa-heading-2",children:s==="create"?"Create New Todo":"Edit Todo"}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-500 hover:text-fa-gray-700 rounded-lg hover:bg-fa-gray-100",children:l.jsx(St,{className:"w-5 h-5"})})]}),a&&l.jsxs(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm flex items-center space-x-2",children:[l.jsx(Cd,{className:"w-4 h-4"}),l.jsx("span",{children:a})]}),l.jsxs("form",{onSubmit:m(T),className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Title *"}),l.jsx("input",{id:"title",type:"text",...p("title",{required:"Title is required",minLength:{value:1,message:"Title cannot be empty"},maxLength:{value:200,message:"Title is too long"}}),className:`fa-input ${C.title?"border-red-500":""}`,placeholder:"What needs to be done?",autoFocus:!0}),C.title&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:C.title.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Description"}),l.jsx("textarea",{id:"description",...p("description"),className:"fa-input min-h-[100px] resize-y",placeholder:"Add more details...",rows:4})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"priority",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Priority"}),l.jsx(Qi,{name:"priority",control:y,render:({field:A})=>l.jsxs("div",{className:"space-y-2",children:[l.jsx("select",{...A,className:"fa-input",children:Mj.map(R=>l.jsx("option",{value:R.value,children:R.label},R.value))}),l.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-fa-gray-50 rounded-lg",children:[l.jsx("span",{className:"text-sm text-fa-gray-600",children:"Preview:"}),l.jsx(Xo,{priority:A.value,variant:"chip",size:"sm",showLabel:!0,animated:!0})]})]})})]}),s==="edit"&&l.jsxs("div",{children:[l.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Status"}),l.jsx(Qi,{name:"status",control:y,render:({field:A})=>l.jsx("select",{...A,className:"fa-input",children:Rj.map(R=>l.jsx("option",{value:R.value,children:R.label},R.value))})})]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Jt,{className:"w-4 h-4 inline mr-1"}),"Category"]}),l.jsx(Qi,{name:"category_id",control:y,render:({field:A})=>l.jsx(Nj,{categories:g,selectedCategoryId:A.value,onSelect:A.onChange,onCreateNew:()=>d(!0),placeholder:"Select a category..."})})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsxs("label",{htmlFor:"due_date",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Ln,{className:"w-4 h-4 inline mr-1"}),"Due Date"]}),l.jsx("input",{id:"due_date",type:"date",...p("due_date"),className:"fa-input"})]}),l.jsxs("div",{children:[l.jsxs("label",{htmlFor:"reminder_at",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Tm,{className:"w-4 h-4 inline mr-1"}),"Reminder"]}),l.jsx("input",{id:"reminder_at",type:"datetime-local",...p("reminder_at"),className:"fa-input"})]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(mr,{className:"w-4 h-4 inline mr-1"}),"Tags"]}),l.jsx(Qi,{name:"tags",control:y,render:({field:A})=>l.jsx(_j,{tags:A.value,onChange:A.onChange,suggestions:v(),placeholder:"Add tags...",maxTags:10,enableSmartSuggestions:!0})})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Tm,{className:"w-4 h-4 inline mr-1"}),"Estimated Duration"]}),l.jsxs("div",{className:"flex space-x-4 items-center",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"number",...p("estimated_duration_hours",{min:{value:0,message:"Hours must be positive"},max:{value:999,message:"Hours must be less than 1000"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"999"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"hours"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"number",...p("estimated_duration_minutes",{min:{value:0,message:"Minutes must be positive"},max:{value:59,message:"Minutes must be less than 60"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"59"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"minutes"})]})]}),(C.estimated_duration_hours||C.estimated_duration_minutes)&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:((D=C.estimated_duration_hours)==null?void 0:D.message)||((I=C.estimated_duration_minutes)==null?void 0:I.message)})]}),l.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-fa-gray-200",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"px-6 py-3 fa-button-glass disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:i||!j,className:"px-6 py-3 fa-button-primary text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:i?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),l.jsx("span",{children:s==="create"?"Creating...":"Saving..."})]}):l.jsxs(l.Fragment,{children:[l.jsx(Ed,{className:"w-4 h-4"}),l.jsx("span",{children:s==="create"?"Create Todo":"Save Changes"})]})})]})]})]})},"todo-form-modal")}),l.jsx(Dj,{isOpen:c,onClose:()=>d(!1),onSubmit:E,mode:"create"})]}):null},Lv=({todo:e,onUpdate:t,onDelete:n})=>{var A;const[r,s]=w.useState(!1),[i,o]=w.useState(!1),[a,u]=w.useState(e.title),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!1),{categories:x}=Pa(),S=x.find(R=>R.id===e.category_id),p=e.status==="completed",m=Gu(e),y=m.isOverdue,b=PC(m.severity),C=w.useCallback(async()=>{if(!c){d(!0);try{const R=await He.toggleTodoCompletion(e.id,e.status);t==null||t(R)}catch(R){console.error("Failed to toggle todo completion:",R)}finally{d(!1)}}},[e.id,e.status,c,t]),j=w.useCallback(async()=>{if(!c){d(!0);try{await He.deleteTodo(e.id),n==null||n(e.id),f(!1)}catch(R){console.error("Failed to delete todo:",R)}finally{d(!1)}}},[e.id,c,n]),_=()=>{v(!0)},T=R=>{t==null||t(R),v(!1)},E=w.useCallback(async()=>{if(c||a.trim()===e.title){o(!1);return}d(!0);try{const R=await He.updateTodo(e.id,{title:a.trim()});t==null||t(R),o(!1)}catch(R){console.error("Failed to update todo:",R),u(e.title)}finally{d(!1)}},[e.id,e.title,a,c,t]),D=()=>{u(e.title),o(!1)},I=()=>{switch(e.status){case"completed":return"text-fa-success";case"in_progress":return"text-fa-info";case"pending":return"text-fa-gray-500";case"cancelled":return"text-fa-error";case"archived":return"text-fa-gray-400";default:return"text-fa-gray-500"}};return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:`fa-todo-card relative transition-all duration-300 ${p?"opacity-70":""} ${y?`${b.borderClass} ${b.bgClass} ${b.pulseClass}`:""} ${fo(e.priority)&&!y?"ring-2 ring-red-500/30 shadow-lg shadow-red-500/20":""} ${Om(e.priority)&&!fo(e.priority)&&!y?"ring-1 ring-orange-500/20":""}`,onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),children:[l.jsxs("div",{className:"flex items-start",children:[l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:C,disabled:c,className:`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 transition-all duration-200 ${p?"bg-fa-success border-fa-success text-white":"border-fa-gray-300 hover:border-fa-blue-400"} ${c?"opacity-50 cursor-not-allowed":""}`,children:c?l.jsx("div",{className:"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"}):p&&l.jsx(Wo,{className:"w-4 h-4"})}),l.jsx("div",{className:"flex-1 min-w-0",children:i?l.jsxs("div",{className:"space-y-2",children:[l.jsx("input",{type:"text",value:a,onChange:R=>u(R.target.value),onKeyDown:R=>{R.key==="Enter"&&E(),R.key==="Escape"&&D()},className:"w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1",autoFocus:!0,disabled:c}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:E,disabled:c,className:"fa-button-glass text-xs px-2 py-1",children:"Save"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:D,disabled:c,className:"fa-button-glass text-xs px-2 py-1",children:"Cancel"})]})]}):l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsx("h3",{className:`text-lg font-medium flex-1 ${p?"line-through text-fa-gray-500":"text-fa-gray-800"}`,children:e.title}),y&&l.jsxs("div",{className:`flex items-center ml-2 ${b.textClass}`,children:[m.severity==="slight"&&l.jsx(Cd,{className:`w-4 h-4 mr-1 ${b.iconClass}`}),m.severity==="moderate"&&l.jsx(Ta,{className:`w-4 h-4 mr-1 ${b.iconClass}`}),m.severity==="severe"&&l.jsx(iT,{className:`w-4 h-4 mr-1 ${b.iconClass} ${b.pulseClass}`}),l.jsx("span",{className:"fa-caption font-medium",children:m.formattedDuration})]})]}),e.description&&l.jsx("p",{className:"fa-body text-fa-gray-600 mt-1 line-clamp-2",children:e.description}),l.jsxs("div",{className:"flex items-center space-x-4 mt-2 flex-wrap gap-2",children:[l.jsx("span",{className:`fa-caption px-2 py-1 rounded-full bg-fa-white-glass ${I()}`,children:((A=e.status)==null?void 0:A.replace("_"," "))||"pending"}),S&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:S.color}}),l.jsx("span",{className:"fa-caption text-fa-gray-600",children:S.name})]}),e.tags&&e.tags.length>0&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(mr,{className:"w-3 h-3 text-fa-gray-400"}),e.tags.slice(0,3).map((R,X)=>l.jsx("span",{className:"fa-caption bg-fa-blue-100 text-fa-blue-700 px-2 py-1 rounded-full",children:R},X)),e.tags.length>3&&l.jsxs("span",{className:"fa-caption text-fa-gray-400",children:["+",e.tags.length-3]})]}),e.due_date&&l.jsxs("div",{className:`flex items-center ${y?b.textClass:"text-fa-gray-500"}`,children:[l.jsx(Ln,{className:"w-4 h-4 mr-1"}),l.jsx("span",{className:"fa-caption",children:He.formatDueDate(e.due_date)})]}),l.jsx(Xo,{priority:e.priority,variant:"badge",size:"sm",showLabel:!1,animated:!0})]})]})}),!i&&l.jsxs(P.div,{className:"flex items-center space-x-1 ml-2",initial:{opacity:0,width:0},animate:{opacity:r?1:0,width:r?"auto":0},transition:{duration:.2},children:[l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:_,disabled:c,className:"p-1 text-fa-gray-400 hover:text-fa-blue-500 disabled:opacity-50",title:"Edit todo",children:l.jsx(aT,{className:"w-4 h-4"})}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>f(!0),disabled:c,className:"p-1 text-fa-gray-400 hover:text-fa-error disabled:opacity-50",title:"Delete todo",children:l.jsx(mT,{className:"w-4 h-4"})})]})]}),Om(e.priority)&&l.jsx("div",{className:"absolute top-0 left-0 w-full rounded-t-2xl",children:l.jsx(Xo,{priority:e.priority,variant:"bar",animated:!0,className:"rounded-t-2xl"})}),fo(e.priority)&&l.jsx("div",{className:"absolute -top-1 -left-1 -right-1 -bottom-1 rounded-2xl bg-gradient-to-r from-red-500/20 to-red-600/20 blur-sm -z-10 animate-pulse"})]}),l.jsx(xv,{isOpen:h,onClose:()=>f(!1),onConfirm:j,title:"Delete Todo",message:`Are you sure you want to delete "${e.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger",isLoading:c}),l.jsx(Rv,{isOpen:g,onClose:()=>v(!1),onSubmit:T,todo:e,mode:"edit"})]})};function Lj(e,t){const{itemHeight:n,containerHeight:r,overscan:s=5,threshold:i=100}=t,[o,a]=w.useState(0),u=w.useRef(null),c=w.useMemo(()=>e.length*n,[e.length,n]),d=w.useMemo(()=>{const m=Math.max(0,Math.floor(o/n)-s),y=Math.ceil(r/n),b=Math.min(e.length-1,m+y+s*2);return{start:m,end:b}},[o,n,r,s,e.length]),h=w.useMemo(()=>{const m=[];for(let y=d.start;y<=d.end;y++)y>=0&&y<e.length&&m.push({index:y,item:e[y],offsetY:y*n,isVisible:!0});return m},[e,d,n]),f=w.useCallback(m=>{const y=m.currentTarget.scrollTop;a(y)},[]),g=w.useCallback((m,y="start")=>{if(!u.current||m<0||m>=e.length)return;let b;switch(y){case"start":b=m*n;break;case"center":b=m*n-(r-n)/2;break;case"end":b=m*n-r+n;break}b=Math.max(0,Math.min(b,c-r)),u.current.scrollTo({top:b,behavior:"smooth"})},[e.length,n,r,c]),v=w.useCallback(()=>{u.current&&u.current.scrollTo({top:0,behavior:"smooth"})},[]),x=w.useCallback(()=>{u.current&&u.current.scrollTo({top:c-r,behavior:"smooth"})},[c,r]);w.useEffect(()=>{if(!u.current)return;const m=o/Math.max(1,c-r),y=Math.max(0,c-r),b=m*y;Math.abs(b-o)>i&&(u.current.scrollTop=b,a(b))},[e.length,c,r,i]);const S=w.useMemo(()=>({style:{height:r,overflowY:"auto",overflowX:"hidden"},onScroll:f,ref:u}),[r,f]),p=w.useMemo(()=>({style:{height:c,position:"relative"}}),[c]);return{virtualItems:h,totalHeight:c,scrollToIndex:g,scrollToTop:v,scrollToBottom:x,containerProps:S,contentProps:p}}const Wd=120,Vj=5,Vv=w.memo(({todo:e,index:t,offsetY:n,onUpdate:r,onDelete:s})=>l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.02,duration:.3},style:{position:"absolute",top:n,left:0,right:0,height:Wd,paddingBottom:12},children:l.jsx(Lv,{todo:e,onUpdate:r,onDelete:s})}));Vv.displayName="VirtualizedTodoItem";const Iv=({todos:e,onTodoUpdate:t,onTodoDelete:n,containerHeight:r,itemHeight:s=Wd,overscan:i=Vj,className:o="",emptyState:a,loadingState:u,isLoading:c=!1})=>{const d=e.length>50,h=Lj(e,{itemHeight:s,containerHeight:r,overscan:i}),f=w.useCallback(v=>{t(v)},[t]),g=w.useCallback(v=>{n(v)},[n]);return c&&u?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:u}):e.length===0&&a?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:a}):e.length===0?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks found"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Try adjusting your filters or add a new task"})]})}):d?l.jsx("div",{className:o,children:l.jsx("div",{...h.containerProps,children:l.jsx("div",{...h.contentProps,children:h.virtualItems.map(({item:v,index:x,offsetY:S})=>l.jsx(Vv,{todo:v,index:x,offsetY:S,onUpdate:f,onDelete:g},v.id))})})}):l.jsx("div",{className:`overflow-y-auto ${o}`,style:{height:r},children:l.jsx("div",{className:"space-y-3 p-1",children:e.map((v,x)=>l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:x*.02,duration:.3},children:l.jsx(Lv,{todo:v,onUpdate:f,onDelete:g})},v.id))})})},Ij=w.memo(({onPerformanceMetrics:e,...t})=>{const n=performance.now();return Z.useEffect(()=>{const s=performance.now()-n;e==null||e({renderTime:s,itemCount:t.todos.length,visibleItems:Math.min(t.todos.length,Math.ceil(t.containerHeight/(t.itemHeight||Wd)))})}),l.jsx(Iv,{...t})});Ij.displayName="VirtualizedTodoListWithMetrics";const Fj=({isVisible:e,selectedCount:t,totalCount:n,isAllSelected:r,isPartiallySelected:s,onSelectAll:i,onSelectNone:o,operations:a,isProcessing:u,onClose:c,className:d=""})=>{var b,C,j,_,T;const[h,f]=w.useState(!1),[g,v]=w.useState({isOpen:!1}),x=async E=>{f(!1),E.requiresConfirmation?v({isOpen:!0,operation:E}):await S(E)},S=async E=>{try{const D=await E.action([]);console.log("Bulk operation result:",D),D.success&&c()}catch(D){console.error("Bulk operation failed:",D)}},p=async()=>{g.operation&&await S(g.operation),v({isOpen:!1})},m=()=>r?l.jsx(fT,{className:"w-5 h-5 text-fa-blue-600"}):s?l.jsx(Nm,{className:"w-5 h-5 text-fa-blue-600 fill-current opacity-50"}):l.jsx(Nm,{className:"w-5 h-5 text-fa-gray-400"}),y=()=>{r?o():i()};return e?l.jsxs(l.Fragment,{children:[l.jsx(ae,{children:l.jsxs(P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted border border-fa-blue-200 shadow-lg ${d}`,children:[l.jsxs("div",{className:"flex items-center justify-between p-4",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:y,className:"flex items-center space-x-2 text-fa-gray-700 hover:text-fa-blue-600",disabled:u,children:[m(),l.jsx("span",{className:"text-sm font-medium",children:r?"Deselect All":"Select All"})]}),l.jsxs("div",{className:"text-sm text-fa-gray-600",children:[l.jsx("span",{className:"font-medium text-fa-blue-600",children:t})," ","of"," ",l.jsx("span",{className:"font-medium",children:n})," ","selected"]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"flex items-center space-x-1",children:a.slice(0,3).map(E=>l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>x(E),disabled:u||t===0,className:`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${E.variant==="danger"?"bg-fa-error text-white hover:bg-red-600 disabled:bg-fa-gray-300":E.variant==="warning"?"bg-fa-warning text-white hover:bg-yellow-600 disabled:bg-fa-gray-300":"fa-button-glass disabled:opacity-50"} disabled:cursor-not-allowed`,title:E.description,children:[l.jsx("span",{children:E.icon}),l.jsx("span",{className:"hidden sm:inline",children:E.name})]},E.id))}),a.length>3&&l.jsxs("div",{className:"relative",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f(!h),disabled:u||t===0,className:"fa-button-glass p-2 disabled:opacity-50 disabled:cursor-not-allowed",children:l.jsx(Jk,{className:"w-4 h-4"})}),l.jsx(ae,{children:h&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full right-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:l.jsx("div",{className:"p-2",children:a.slice(3).map(E=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>x(E),disabled:u,className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed ${E.variant==="danger"?"text-fa-error hover:bg-red-50":E.variant==="warning"?"text-fa-warning hover:bg-yellow-50":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsx("span",{className:"text-lg",children:E.icon}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium",children:E.name}),l.jsx("div",{className:"text-xs text-fa-gray-500",children:E.description})]})]},E.id))})})})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:u,children:l.jsx(St,{className:"w-4 h-4"})})]})]}),u&&l.jsx(P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"border-t border-fa-gray-200 px-4 py-3",children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"Processing bulk operation..."})]})})]})}),l.jsx(xv,{isOpen:g.isOpen,onClose:()=>v({isOpen:!1}),onConfirm:p,title:`${(b=g.operation)==null?void 0:b.name}`,message:((C=g.operation)==null?void 0:C.confirmationMessage)||`Are you sure you want to ${(j=g.operation)==null?void 0:j.name.toLowerCase()} ${t} selected todo${t!==1?"s":""}?`,confirmText:((_=g.operation)==null?void 0:_.name)||"Confirm",cancelText:"Cancel",variant:((T=g.operation)==null?void 0:T.variant)||"info",isLoading:u})]}):null},_l=[{id:"default",name:"Default",config:{field:"position",order:"asc"},icon:"📋",description:"Custom order"},{id:"priority_high",name:"Priority (High to Low)",config:{field:"priority",order:"desc"},icon:"🔥",description:"Most important first"},{id:"due_date_soon",name:"Due Date (Soonest)",config:{field:"due_date",order:"asc"},icon:"⏰",description:"Upcoming deadlines first"},{id:"alphabetical",name:"Alphabetical",config:{field:"title",order:"asc"},icon:"🔤",description:"A to Z"},{id:"recently_created",name:"Recently Created",config:{field:"created_at",order:"desc"},icon:"🆕",description:"Newest first"},{id:"recently_updated",name:"Recently Updated",config:{field:"updated_at",order:"desc"},icon:"📝",description:"Last modified first"}],rp={very_high:5,high:4,medium:3,low:2,very_low:1},sp={in_progress:5,pending:4,completed:3,archived:2,cancelled:1};function Oj(e){const[t,n]=w.useState({field:"position",order:"asc"}),[r,s]=w.useState("default"),i=w.useMemo(()=>[...e].sort((v,x)=>{let S=0;switch(t.field){case"title":S=v.title.localeCompare(x.title);break;case"status":S=sp[v.status]-sp[x.status];break;case"priority":S=rp[v.priority]-rp[x.priority];break;case"due_date":!v.due_date&&!x.due_date?S=0:v.due_date?x.due_date?S=new Date(v.due_date).getTime()-new Date(x.due_date).getTime():S=-1:S=1;break;case"created_at":S=new Date(v.created_at).getTime()-new Date(x.created_at).getTime();break;case"updated_at":S=new Date(v.updated_at).getTime()-new Date(x.updated_at).getTime();break;case"position":S=v.position-x.position;break;case"estimated_duration":const p=v.estimated_duration?parseInt(v.estimated_duration):0,m=x.estimated_duration?parseInt(x.estimated_duration):0;S=p-m;break;default:S=0}return t.order==="desc"?-S:S}),[e,t]),o=w.useCallback((g,v)=>{n(x=>({field:g,order:v||(x.field===g&&x.order==="asc"?"desc":"asc")})),s("")},[]),a=w.useCallback(g=>{const v=_l.find(x=>x.id===g);v&&(n(v.config),s(g))},[]),u=w.useCallback(()=>{n(g=>({...g,order:g.order==="asc"?"desc":"asc"})),s("")},[]),c=w.useCallback(g=>t.field!==g?null:t.order==="asc"?"↑":"↓",[t]),d=w.useCallback(g=>t.field===g,[t.field]),h=w.useMemo(()=>{const g=_l.find(S=>S.id===r);if(g)return g.description||g.name;const v=t.field.replace("_"," "),x=t.order==="asc"?"ascending":"descending";return`${v} (${x})`},[t,r]),f=w.useCallback((g,v="asc")=>{console.log("Secondary sort not yet implemented:",g,v)},[]);return{sortConfig:t,sortedTodos:i,updateSort:o,applyPreset:a,toggleSortOrder:u,getSortIndicator:c,isSortedBy:d,sortDescription:h,presets:_l,activePreset:r,addSecondarySort:f}}const El={search:"",status:[],priority:[],tags:[],dueDate:"all",category:[],isCompleted:void 0},ip=[{id:"all",name:"All Tasks",filters:{},icon:"📋"},{id:"active",name:"Active",filters:{status:["pending","in_progress"]},icon:"⚡"},{id:"completed",name:"Completed",filters:{status:["completed"]},icon:"✅"},{id:"overdue",name:"Overdue",filters:{dueDate:"overdue"},icon:"🚨"},{id:"high_priority",name:"High Priority",filters:{priority:["high","very_high"]},icon:"🔥"},{id:"today",name:"Due Today",filters:{dueDate:"today"},icon:"📅"}];function zj(e,t=[]){const[n,r]=w.useState(El),[s,i]=w.useState("all"),o=w.useMemo(()=>e.filter(f=>{var g;if(n.search){const v=n.search.toLowerCase();if(!(f.title.toLowerCase().includes(v)||((g=f.description)==null?void 0:g.toLowerCase().includes(v))||f.tags.some(S=>S.toLowerCase().includes(v))))return!1}if(n.status.length>0&&!n.status.includes(f.status)||n.priority.length>0&&!n.priority.includes(f.priority)||n.tags.length>0&&!n.tags.some(x=>f.tags.some(S=>S.toLowerCase().includes(x.toLowerCase())))||n.category.length>0&&(!f.category_id||!n.category.includes(f.category_id)))return!1;if(n.dueDate!=="all"){const v=new Date,x=new Date(v.getFullYear(),v.getMonth(),v.getDate()),S=new Date(x);S.setDate(S.getDate()+1);const p=new Date(x);p.setDate(p.getDate()+7);const m=new Date(x);switch(m.setMonth(m.getMonth()+1),n.dueDate){case"overdue":if(!f.due_date||new Date(f.due_date)>=x||f.status==="completed")return!1;break;case"today":if(!f.due_date)return!1;const y=new Date(f.due_date);if(y<x||y>=S)return!1;break;case"tomorrow":if(!f.due_date)return!1;const b=new Date(f.due_date);if(b<S||b>=new Date(S.getTime()+24*60*60*1e3))return!1;break;case"this_week":if(!f.due_date||new Date(f.due_date)>p)return!1;break;case"this_month":if(!f.due_date||new Date(f.due_date)>m)return!1;break;case"no_due_date":if(f.due_date)return!1;break}}if(n.isCompleted!==void 0){const v=f.status==="completed";if(n.isCompleted!==v)return!1}return!0}),[e,n]),a=w.useCallback((f,g)=>{r(v=>({...v,[f]:g})),i("")},[]),u=w.useCallback(f=>{const g=ip.find(v=>v.id===f);g&&(r(v=>({...El,...v,...g.filters})),i(f))},[]),c=w.useCallback(()=>{r(El),i("all")},[]),d=w.useMemo(()=>{const f=[];return n.search&&f.push(`Search: "${n.search}"`),n.status.length>0&&f.push(`Status: ${n.status.join(", ")}`),n.priority.length>0&&f.push(`Priority: ${n.priority.join(", ")}`),n.tags.length>0&&f.push(`Tags: ${n.tags.join(", ")}`),n.dueDate!=="all"&&f.push(`Due: ${n.dueDate.replace("_"," ")}`),n.category.length>0&&f.push(`Category: ${n.category.join(", ")}`),{activeFilters:f,hasActiveFilters:f.length>0,totalFiltered:o.length,totalOriginal:e.length}},[n,o.length,e.length]),h=w.useMemo(()=>{const f=[...new Set(e.map(x=>x.status))],g=[...new Set(e.map(x=>x.priority))],v=[...new Set(e.flatMap(x=>x.tags))];return{statuses:f,priorities:g,tags:v,categories:t}},[e,t]);return{filters:n,filteredTodos:o,updateFilter:a,applyPreset:u,clearFilters:c,filterSummary:d,filterOptions:h,presets:ip,activePreset:s}}function $j(e,t,n){const[r,s]=w.useState(new Set),[i,o]=w.useState(!1),[a,u]=w.useState(null),c=w.useMemo(()=>e.filter(j=>r.has(j.id)),[e,r]),d=w.useCallback(j=>{s(_=>new Set([..._,j]))},[]),h=w.useCallback(j=>{s(_=>{const T=new Set(_);return T.delete(j),T})},[]),f=w.useCallback(j=>{s(_=>{const T=new Set(_);return T.has(j)?T.delete(j):T.add(j),T})},[]),g=w.useCallback(()=>{s(new Set(e.map(j=>j.id)))},[e]),v=w.useCallback(()=>{s(new Set)},[]),x=w.useCallback(j=>{const _=e.filter(T=>T.status===j).map(T=>T.id);s(new Set(_))},[e]),S=w.useCallback(j=>{const _=e.filter(T=>T.priority===j).map(T=>T.id);s(new Set(_))},[e]),p=w.useCallback(async j=>{const _=Array.from(r),T={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u(`update_status_${j}`);try{const E=_.map(async A=>{try{return{success:!0,todo:await He.updateTodoStatus(A,j)}}catch(R){return{success:!1,error:R instanceof Error?R.message:"Unknown error",todoId:A}}}),D=await Promise.all(E),I=[];D.forEach(A=>{A.success&&"todo"in A?(I.push(A.todo),A.processedCount++):!A.success&&"error"in A&&(A.failedCount++,A.errors.push(`Failed to update todo ${A.todoId}: ${A.error}`))}),t(I),s(new Set),T.processedCount=I.length,T.failedCount=D.length-I.length,T.success=T.failedCount===0}catch(E){T.success=!1,T.errors.push(E instanceof Error?E.message:"Unknown error")}finally{o(!1)}return T},[r,t]),m=w.useCallback(async j=>{const _=Array.from(r),T={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u(`update_priority_${j}`);try{const E=_.map(async A=>{try{return{success:!0,todo:await He.updateTodo(A,{priority:j})}}catch(R){return{success:!1,error:R instanceof Error?R.message:"Unknown error",todoId:A}}}),D=await Promise.all(E),I=[];D.forEach(A=>{A.success&&"todo"in A?(I.push(A.todo),A.processedCount++):!A.success&&"error"in A&&(A.failedCount++,A.errors.push(`Failed to update todo ${A.todoId}: ${A.error}`))}),t(I),s(new Set),T.processedCount=I.length,T.failedCount=D.length-I.length,T.success=T.failedCount===0}catch(E){T.success=!1,T.errors.push(E instanceof Error?E.message:"Unknown error")}finally{o(!1)}return T},[r,t]),y=w.useCallback(async()=>{const j=Array.from(r),_={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u("delete");try{const T=j.map(async D=>{try{return await He.deleteTodo(D),{success:!0,todoId:D}}catch(I){return{success:!1,error:I instanceof Error?I.message:"Unknown error",todoId:D}}}),E=await Promise.all(T);E.forEach(D=>{D.success?(n(D.todoId),D.processedCount++):(D.failedCount++,D.errors.push(`Failed to delete todo ${D.todoId}: ${D.error}`))}),s(new Set),_.processedCount=E.filter(D=>D.success).length,_.failedCount=E.filter(D=>!D.success).length,_.success=_.failedCount===0}catch(T){_.success=!1,_.errors.push(T instanceof Error?T.message:"Unknown error")}finally{o(!1)}return _},[r,n]),b=w.useMemo(()=>[{id:"mark_completed",name:"Mark as Completed",icon:"✅",description:"Mark selected todos as completed",action:()=>p("completed")},{id:"mark_pending",name:"Mark as Pending",icon:"⏳",description:"Mark selected todos as pending",action:()=>p("pending")},{id:"mark_in_progress",name:"Mark as In Progress",icon:"🔄",description:"Mark selected todos as in progress",action:()=>p("in_progress")},{id:"set_high_priority",name:"Set High Priority",icon:"🔥",description:"Set selected todos to high priority",action:()=>m("high")},{id:"set_medium_priority",name:"Set Medium Priority",icon:"⚡",description:"Set selected todos to medium priority",action:()=>m("medium")},{id:"set_low_priority",name:"Set Low Priority",icon:"📝",description:"Set selected todos to low priority",action:()=>m("low")},{id:"delete",name:"Delete",icon:"🗑️",description:"Delete selected todos permanently",action:y,requiresConfirmation:!0,confirmationMessage:"Are you sure you want to delete the selected todos? This action cannot be undone.",variant:"danger"}],[p,m,y]),C=w.useMemo(()=>({selectedCount:r.size,totalCount:e.length,hasSelection:r.size>0,isAllSelected:r.size===e.length&&e.length>0,isPartiallySelected:r.size>0&&r.size<e.length}),[r.size,e.length]);return{selectedTodoIds:r,selectedTodos:c,selectionState:C,selectTodo:d,deselectTodo:h,toggleTodoSelection:f,selectAll:g,selectNone:v,selectByStatus:x,selectByPriority:S,availableOperations:b,bulkUpdateStatus:p,bulkUpdatePriority:m,bulkDelete:y,isProcessing:i,lastOperation:a}}const Uj=({containerHeight:e=600,className:t=""})=>{const{todos:n,categories:r,isLoading:s,error:i,filters:o,selectedTodos:a,loadTodos:u,loadCategories:c,updateTodo:d,deleteTodo:h,setSearchQuery:f,setFilters:g,selectTodo:v,deselectTodo:x,clearSelection:S}=Pa(),{todoViewMode:p,setTodoViewMode:m}=_C(),[y,b]=w.useState(!1),[C,j]=w.useState(new Date),_=IC(o.searchQuery,300),T=w.useCallback(async U=>{try{await d(U.id,U)}catch(L){console.error("Failed to update todo:",L)}},[d]),E=w.useCallback(async U=>{try{await h(U)}catch(L){console.error("Failed to delete todo:",L)}},[h]),D=w.useCallback(async U=>{try{for(const L of U)await d(L.id,L)}catch(L){console.error("Failed to bulk update todos:",L)}},[d]),I=zj(n,r),A=Oj(I.filteredTodos),R=$j(n,D,E);w.useEffect(()=>{u(),c()},[u,c]),w.useEffect(()=>{f(_)},[_,f]);const X=w.useCallback(async()=>{try{await u(),j(new Date)}catch(U){console.error("Failed to refresh todos:",U)}},[u]),G=w.useMemo(()=>{const U=new Set;return n.forEach(L=>{L.title.split(" ").forEach(W=>{W.length>2&&U.add(W)}),L.tags.forEach(W=>U.add(W))}),Array.from(U).slice(0,10)},[n]),H=w.useCallback(U=>{m(U)},[m]);return s?l.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-8 h-8 border-4 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin mx-auto mb-4"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Loading todos..."})]})}):i?l.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"⚠️"}),l.jsx("h3",{className:"fa-heading-3 mb-2 text-fa-error",children:"Error Loading Todos"}),l.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:i}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:u,className:"fa-button-primary px-4 py-2",children:"Try Again"})]})}):l.jsxs("div",{className:`w-full h-full flex flex-col ${t}`,children:[l.jsxs("div",{className:"flex-shrink-0 space-y-4 mb-6",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("div",{className:"flex-1",children:l.jsx(OC,{value:o.searchQuery,onChange:f,onFilterToggle:()=>b(!y),suggestions:G,isFilterActive:Object.values(o).some(U=>Array.isArray(U)?U.length>0:typeof U=="string"?U!=="":typeof U=="object"&&U!==null?Object.keys(U).length>0:!1)})}),l.jsx("div",{className:"flex items-center space-x-1 fa-glass-panel px-2 py-1 rounded-lg",children:["list","grid","kanban"].map(U=>l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>H(U),className:`p-2 rounded-lg transition-all duration-200 ${p===U?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,title:`${U.charAt(0).toUpperCase()+U.slice(1)} view`,children:[U==="list"&&l.jsx(nv,{className:"w-4 h-4"}),U==="grid"&&l.jsx(tT,{className:"w-4 h-4"}),U==="kanban"&&l.jsx(Yk,{className:"w-4 h-4"})]},U))}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:X,disabled:s,className:"fa-button-glass p-2 disabled:opacity-50",title:"Refresh todos",children:l.jsx(sv,{className:`w-4 h-4 ${s?"animate-spin":""}`})})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx(KC,{sortConfig:A.sortConfig,onUpdateSort:A.updateSort,onApplyPreset:A.applyPreset,onToggleSortOrder:A.toggleSortOrder,presets:A.presets,activePreset:A.activePreset,sortDescription:A.sortDescription}),l.jsx("div",{className:"text-sm text-fa-gray-500",children:I.filterSummary.hasActiveFilters?l.jsxs("span",{children:[I.filterSummary.totalFiltered," of ",I.filterSummary.totalOriginal," tasks"]}):l.jsxs("span",{children:[n.length," tasks"]})})]})]}),l.jsx(ae,{children:R.selectionState.hasSelection&&l.jsx("div",{className:"flex-shrink-0 mb-4",children:l.jsx(Fj,{isVisible:R.selectionState.hasSelection,selectedCount:R.selectionState.selectedCount,totalCount:R.selectionState.totalCount,isAllSelected:R.selectionState.isAllSelected,isPartiallySelected:R.selectionState.isPartiallySelected,onSelectAll:R.selectAll,onSelectNone:R.selectNone,operations:R.availableOperations,isProcessing:R.isProcessing,onClose:R.selectNone})})}),l.jsx("div",{className:"flex-1 min-h-0",children:l.jsx(Iv,{todos:A.sortedTodos,onTodoUpdate:T,onTodoDelete:E,containerHeight:e-200,isLoading:s,emptyState:I.filterSummary.hasActiveFilters?l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🔍"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No matching tasks"}),l.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:"Try adjusting your filters or search terms"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:I.clearFilters,className:"fa-button-glass px-4 py-2",children:"Clear Filters"})]}):l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks yet"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Add your first task to get started!"})]})})}),l.jsx(WC,{isOpen:y,onClose:()=>b(!1),filters:I.filters,onUpdateFilter:I.updateFilter,onApplyPreset:I.applyPreset,onClearFilters:I.clearFilters,presets:I.presets,activePreset:I.activePreset,filterOptions:I.filterOptions,filterSummary:I.filterSummary})]})},Bj=({onTodoCreate:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(!1),[i,o]=w.useState(!1),a=d=>{d.preventDefault(),t.trim()&&o(!0)},u=d=>{e==null||e(d),n(""),o(!1)},c=()=>{o(!0)};return l.jsxs(l.Fragment,{children:[l.jsx("form",{onSubmit:a,className:"w-full",children:l.jsx("div",{className:`fa-glass-panel transition-all duration-300 ${r?"ring-2 ring-fa-blue-400 shadow-lg":"shadow-md"}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-4 text-fa-blue-500 hover:text-fa-blue-600",children:l.jsx(ka,{className:"w-5 h-5"})}),l.jsx("input",{type:"text",value:t,onChange:d=>n(d.target.value),onFocus:()=>s(!0),onBlur:()=>s(!1),onClick:c,placeholder:"What needs to be done?",className:"flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none cursor-pointer",readOnly:!0}),l.jsxs("div",{className:"flex items-center space-x-2 pr-4",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(Ln,{className:"w-4 h-4"})}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(mr,{className:"w-4 h-4"})})]})]})})}),l.jsx(Rv,{isOpen:i,onClose:()=>o(!1),onSubmit:u,mode:"create"})]})},Hj=({systemInfo:e})=>{const{theme:t,toggleTheme:n}=wT(),[r,s]=w.useState(!0);return l.jsxs("div",{className:"w-full h-full flex flex-col bg-transparent",children:[l.jsx(kT,{systemInfo:e,onToggleTheme:n,theme:t}),l.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[l.jsx(P.div,{className:`h-full ${r?"w-64":"w-0"}`,animate:{width:r?256:0},transition:{duration:.3,ease:[.4,0,.2,1]},children:l.jsx(TT,{})}),l.jsx("div",{className:"flex-1 flex flex-col overflow-hidden p-6",children:l.jsxs("div",{className:"fa-glass-panel-frosted flex-1 flex flex-col rounded-2xl p-6",children:[l.jsxs("div",{className:"mb-6",children:[l.jsx("h1",{className:"fa-heading-1 mb-2",children:"My Tasks"}),l.jsx("p",{className:"fa-body text-fa-gray-600",children:"Stay organized and productive"})]}),l.jsx("div",{className:"mb-6",children:l.jsx(Bj,{})}),l.jsx("div",{className:"flex-1 overflow-hidden",children:l.jsx(Uj,{})})]})})]})]})},Wj=()=>l.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-fa-blue-50 to-fa-aqua-50",children:l.jsxs("div",{className:"text-center",children:[l.jsx(P.div,{className:"w-16 h-16 mx-auto mb-6 rounded-full border-4 border-fa-blue-500 border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),l.jsx(P.h1,{className:"fa-heading-1 mb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Modern Todo"}),l.jsx(P.p,{className:"fa-caption text-fa-gray-600",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Loading your productivity experience..."})]})});class Kj extends w.Component{constructor(){super(...arguments);Le(this,"state",{hasError:!1,error:void 0})}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Uncaught error:",n,r)}render(){var n;return this.state.hasError?l.jsx("div",{className:"w-full h-full flex items-center justify-center p-6",children:l.jsxs(P.div,{className:"fa-glass-panel-frosted p-8 max-w-md w-full text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[l.jsx("div",{className:"text-fa-error text-5xl mb-4",children:"⚠️"}),l.jsx("h2",{className:"fa-heading-2 mb-4",children:"Something went wrong"}),l.jsx("p",{className:"fa-body text-fa-gray-600 mb-6",children:((n=this.state.error)==null?void 0:n.message)||"An unexpected error occurred."}),l.jsx("button",{className:"fa-button-primary px-6 py-3 rounded-lg font-medium",onClick:()=>window.location.reload(),children:"Reload Application"})]})}):this.props.children}}const Gj=({onSwitchToRegister:e,onSwitchToReset:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(""),[o,a]=w.useState(!1),[u,c]=w.useState(!1),[d,h]=w.useState(null),{login:f}=is(),g=async v=>{v.preventDefault(),c(!0),h(null);try{await f(n,s)}catch(x){h(x instanceof Error?x.message:"Login failed")}finally{c(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Pn,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Welcome Back"}),l.jsx("p",{className:"fa-body text-gray-600",children:"Sign in to your account"})]}),d&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:d}),l.jsxs(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm",children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("div",{className:"font-medium",children:"🧪 Development Testing Account"}),l.jsx("button",{type:"button",onClick:()=>{r("GOD"),i("123456")},className:"text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 transition-colors",children:"Quick Fill"})]}),l.jsxs("div",{className:"text-xs",children:["Username: ",l.jsx("span",{className:"font-mono font-semibold",children:"GOD"}),l.jsx("br",{}),"Password: ",l.jsx("span",{className:"font-mono font-semibold",children:"123456"})]})]}),l.jsxs("form",{onSubmit:g,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:n,onChange:v=>r(v.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(nr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"password",type:o?"text":"password",value:s,onChange:v=>i(v.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter your password",required:!0}),l.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:o?l.jsx(ui,{className:"h-5 w-5"}):l.jsx(ci,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),l.jsx("div",{className:"text-sm",children:l.jsx("button",{type:"button",onClick:t,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot password?"})})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:u,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:u?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",l.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up"})]})})]})})},qj=({onSwitchToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,o]=w.useState(""),[a,u]=w.useState(""),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!1),[x,S]=w.useState(null),{register:p}=is(),m=async y=>{if(y.preventDefault(),i!==a){S("Passwords do not match");return}v(!0),S(null);try{await p(t,i,r)}catch(b){S(b instanceof Error?b.message:"Registration failed")}finally{v(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Pn,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Create Account"}),l.jsx("p",{className:"fa-body text-gray-600",children:"Join our todo application"})]}),x&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),l.jsxs("form",{onSubmit:m,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:t,onChange:y=>n(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Choose a username",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(_d,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"email",type:"email",value:r,onChange:y=>s(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your email"})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(nr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"password",type:c?"text":"password",value:i,onChange:y=>o(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Create a strong password",required:!0}),l.jsx("button",{type:"button",onClick:()=>d(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:c?l.jsx(ui,{className:"h-5 w-5"}):l.jsx(ci,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(nr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"confirmPassword",type:h?"text":"password",value:a,onChange:y=>u(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm your password",required:!0}),l.jsx("button",{type:"button",onClick:()=>f(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:h?l.jsx(ui,{className:"h-5 w-5"}):l.jsx(ci,{className:"h-5 w-5"})})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",l.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})})},Qj=({onBackToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,o]=w.useState(""),[a,u]=w.useState(!1),[c,d]=w.useState(!1),[h,f]=w.useState("request"),[g,v]=w.useState(!1),[x,S]=w.useState(null),[p,m]=w.useState(null),y=async C=>{C.preventDefault(),v(!0),S(null),m(null);try{await new Promise(j=>setTimeout(j,1e3)),f("reset"),m("Reset instructions sent to your email")}catch{S("Failed to send reset instructions")}finally{v(!1)}},b=async C=>{if(C.preventDefault(),r!==i){S("Passwords do not match");return}if(r.length<12){S("Password must be at least 12 characters long");return}v(!0),S(null),m(null);try{await new Promise(j=>setTimeout(j,1e3)),m("Password reset successfully")}catch{S("Failed to reset password")}finally{v(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(nr,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:h==="request"?"Reset Password":"Set New Password"}),l.jsx("p",{className:"fa-body text-gray-600",children:h==="request"?"Enter your username to receive reset instructions":"Enter your new password"})]}),x&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),p&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm",children:p}),h==="request"?l.jsxs("form",{onSubmit:y,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:t,onChange:C=>n(C.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Sending instructions..."]}):"Send Reset Instructions"})]}):l.jsxs("form",{onSubmit:b,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(nr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"newPassword",type:a?"text":"password",value:r,onChange:C=>s(C.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter new password",required:!0}),l.jsx("button",{type:"button",onClick:()=>u(!a),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:a?l.jsx(ui,{className:"h-5 w-5"}):l.jsx(ci,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(nr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"confirmPassword",type:c?"text":"password",value:i,onChange:C=>o(C.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm new password",required:!0}),l.jsx("button",{type:"button",onClick:()=>d(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:c?l.jsx(ui,{className:"h-5 w-5"}):l.jsx(ci,{className:"h-5 w-5"})})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Resetting password..."]}):"Reset Password"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("button",{onClick:e,className:"flex items-center justify-center mx-auto text-sm text-blue-600 hover:text-blue-500",children:[l.jsx(Qk,{className:"w-4 h-4 mr-1"}),"Back to login"]})})]})})},Xj=()=>{const[e,t]=w.useState("login"),n=()=>{t("register")},r=()=>{t("login")},s=()=>{t("reset")},i=()=>{t("login")};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100 p-4 sm:p-6 lg:p-8",children:l.jsx("div",{className:"w-full max-w-md mx-auto",children:l.jsx(ae,{mode:"wait",children:l.jsxs(P.div,{initial:{opacity:0,x:e==="login"?-20:e==="register"?0:20},animate:{opacity:1,x:0},exit:{opacity:0,x:e==="login"?20:e==="register"?0:-20},transition:{duration:.3},className:"w-full",children:[e==="login"&&l.jsx(Gj,{onSwitchToRegister:n,onSwitchToReset:s}),e==="register"&&l.jsx(qj,{onSwitchToLogin:r}),e==="reset"&&l.jsx(Qj,{onBackToLogin:i})]},e)})})})},Yj=({children:e})=>{const{isAuthenticated:t,loading:n}=is();return n?l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100",children:l.jsxs(P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?l.jsx(l.Fragment,{children:e}):l.jsx(Xj,{})},Zj=()=>{const[e,t]=w.useState(!0),[n,r]=w.useState(null),s=Mv();return w.useEffect(()=>{(async()=>{try{if(LC(),window.electronAPI){const o=await window.electronAPI.system.getInfo();r(o)}await new Promise(o=>setTimeout(o,1500)),t(!1)}catch(o){console.error("Failed to initialize app:",o),s.error("Initialization Failed","Failed to initialize the application. Please try restarting."),t(!1)}})()},[]),e?l.jsx(Wj,{}):l.jsxs(Kj,{children:[l.jsx(gT,{children:l.jsx(xT,{children:l.jsx(ST,{children:l.jsx(Yj,{children:l.jsx(ae,{mode:"wait",children:l.jsx(P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,ease:[.4,0,.2,1]},className:"w-full h-full",children:l.jsx(Hj,{systemInfo:n})},"main-app")})})})})}),l.jsx(Cj,{toasts:s.toasts,onDismiss:s.dismissToast,position:"top-right"})]})},Fv=document.getElementById("root");if(!Fv)throw new Error("Root element not found");Pl.createRoot(Fv).render(l.jsx(Z.StrictMode,{children:l.jsx(Zj,{})}));
